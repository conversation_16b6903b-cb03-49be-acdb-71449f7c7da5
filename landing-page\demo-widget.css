/**
 * Standalone Demo Widget CSS - Copied from Original Plugin
 * Frontend styles for My Accessibility Plugin Demo
 *
 * @package MyAccessibilityPlugin
 * @since 1.0.0
 */

/* Import OpenDyslexic Font from CDN */
@import url('https://cdn.jsdelivr.net/npm/open-dyslexic@1.0.0/open-dyslexic.min.css');

/* ===== PREMIUM FOCUS SYSTEM - KEYBOARD NAVIGATION ONLY ===== */
*:focus {
    outline: none;
}

*:focus-visible {
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3) !important;
    border-color: var(--map-primary) !important;
    transition: all var(--map-transition-base);
}

/* Modern Accessibility Plugin - Unique Design System */
:root {
    /* Primary Brand Colors - Warm & Accessible */
    --map-primary: #6366f1;
    --map-primary-light: #818cf8;
    --map-primary-dark: #4f46e5;
    --map-accent: #f59e0b;
    --map-accent-light: #fbbf24;

    /* Neutral Palette */
    --map-white: #ffffff;
    --map-gray-50: #f9fafb;
    --map-gray-100: #f3f4f6;
    --map-gray-200: #e5e7eb;
    --map-gray-300: #d1d5db;
    --map-gray-400: #9ca3af;
    --map-gray-500: #6b7280;
    --map-gray-600: #4b5563;
    --map-gray-700: #374151;
    --map-gray-800: #1f2937;
    --map-gray-900: #111827;

    /* Semantic Colors */
    --map-success: #059669;
    --map-warning: #d97706;
    --map-error: #dc2626;
    --map-info: #0284c7;

    /* Theme Semantic Variables */
    --map-surface: var(--map-white);
    --map-border: var(--map-gray-300);
    --map-text: var(--map-gray-700);
    --map-text-secondary: var(--map-gray-500);
    --map-bg-rgb: 255, 255, 255;

    /* Design Tokens */
    --map-radius-sm: 6px;
    --map-radius-md: 12px;
    --map-radius-lg: 16px;
    --map-radius-xl: 24px;
    --map-radius-full: 9999px;

    /* Shadows */
    --map-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --map-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --map-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --map-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Transitions */
    --map-transition-fast: 150ms ease-out;
    --map-transition-base: 250ms ease-out;
    --map-transition-slow: 350ms ease-out;

    /* Typography */
    --map-font-size-xs: 0.75rem;
    --map-font-size-sm: 0.875rem;
    --map-font-size-base: 1rem;
    --map-font-size-lg: 1.125rem;
    --map-font-size-xl: 1.25rem;

    /* Spacing */
    --map-space-1: 0.25rem;
    --map-space-2: 0.5rem;
    --map-space-3: 0.75rem;
    --map-space-4: 1rem;
    --map-space-5: 1.25rem;
    --map-space-6: 1.5rem;
    --map-space-8: 2rem;
    --map-space-10: 2.5rem;
    --map-space-12: 3rem;

    /* Z-Index Scale */
    --map-z-dropdown: 1000;
    --map-z-sticky: 1020;
    --map-z-fixed: 1030;
    --map-z-modal: 1040;
    --map-z-popover: 1050;
    --map-z-tooltip: 1060;
    --map-z-toast: 1070;
}

/* ===== THEME VARIANTS ===== */

/* Ocean Theme */
.map-theme-ocean {
    --map-primary: #0ea5e9;
    --map-primary-light: #38bdf8;
    --map-primary-dark: #0284c7;
    --map-accent: #06b6d4;
    --map-accent-light: #22d3ee;
}

/* Forest Theme */
.map-theme-forest {
    --map-primary: #059669;
    --map-primary-light: #10b981;
    --map-primary-dark: #047857;
    --map-accent: #65a30d;
    --map-accent-light: #84cc16;
}

/* Sunset Theme */
.map-theme-sunset {
    --map-primary: #ea580c;
    --map-primary-light: #fb923c;
    --map-primary-dark: #c2410c;
    --map-accent: #dc2626;
    --map-accent-light: #ef4444;
}

/* Purple Theme */
.map-theme-purple {
    --map-primary: #7c3aed;
    --map-primary-light: #8b5cf6;
    --map-primary-dark: #6d28d9;
    --map-accent: #a855f7;
    --map-accent-light: #c084fc;
}

/* ===== ACCESSIBILITY WIDGET - MODERN MINIMAL DESIGN ===== */

.map-accessibility-widget {
    position: fixed;
    z-index: var(--map-z-popover);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Inter', sans-serif;
    font-size: var(--map-font-size-sm);
    line-height: 1.6;
    color: var(--map-gray-700);
    --webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Widget Positioning */
.map-position-top-left {
    top: 20px;
    left: 20px;
}

.map-position-top-right {
    top: 20px;
    right: 20px;
}

.map-position-bottom-left {
    bottom: 20px;
    left: 20px;
}

.map-position-bottom-right {
    bottom: 20px;
    right: 20px;
}

.map-position-center {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

/* Main Toggle Button */
.map-main-toggle {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-dark) 100%);
    border: none;
    border-radius: var(--map-radius-full);
    color: var(--map-white);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--map-shadow-lg);
    transition: all var(--map-transition-base);
    position: relative;
    overflow: hidden;
}

.map-main-toggle:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: var(--map-shadow-xl);
}

.map-main-toggle:active {
    transform: translateY(0) scale(0.98);
}

.map-main-toggle:focus-visible {
    outline: 3px solid rgba(99, 102, 241, 0.4);
    outline-offset: 2px;
}

/* Toggle Icon */
.map-toggle-icon {
    transition: all var(--map-transition-base);
}

.map-panel-open .map-toggle-icon {
    transform: rotate(90deg);
}

/* Widget Panel */
.map-widget-panel {
    position: absolute;
    top: 0;
    left: 0;
    width: 380px;
    height: 500px;
    background: var(--map-white);
    border-radius: var(--map-radius-xl);
    box-shadow: var(--map-shadow-xl);
    border: 1px solid var(--map-gray-200);
    overflow: hidden;
    transform-origin: bottom right;
    animation: panelSlideIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes panelSlideIn {
    from {
        opacity: 0;
        transform: scale(0.8) translateY(20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* Panel Header */
.map-panel-header {
    padding: var(--map-space-4) var(--map-space-5);
    border-bottom: 1px solid var(--map-gray-200);
    background: var(--map-gray-50);
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 60px;
}

.map-panel-title {
    font-size: var(--map-font-size-lg);
    font-weight: 600;
    color: var(--map-gray-900);
    margin: 0;
}

/* Header Navigation */
.map-header-navigation {
    display: flex;
    align-items: center;
    gap: var(--map-space-3);
}

.map-header-back-button {
    background: none;
    border: none;
    color: var(--map-gray-600);
    cursor: pointer;
    padding: var(--map-space-2);
    border-radius: var(--map-radius-sm);
    transition: all var(--map-transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.map-header-back-button:hover {
    background: var(--map-gray-200);
    color: var(--map-gray-900);
}

.map-header-category-title {
    font-size: var(--map-font-size-lg);
    font-weight: 600;
    color: var(--map-gray-900);
    margin: 0;
}

/* Header Buttons */
.map-header-buttons {
    display: flex;
    align-items: center;
    gap: var(--map-space-2);
}

.map-reset-button,
.map-close-button {
    background: none;
    border: none;
    color: var(--map-gray-600);
    cursor: pointer;
    padding: var(--map-space-2);
    border-radius: var(--map-radius-sm);
    transition: all var(--map-transition-fast);
    display: flex;
    align-items: center;
    gap: var(--map-space-1);
    font-size: var(--map-font-size-sm);
}

.map-reset-button:hover,
.map-close-button:hover {
    background: var(--map-gray-200);
    color: var(--map-gray-900);
}

/* Panel Content */
.map-panel-content {
    height: calc(100% - 60px);
    position: relative;
    overflow: hidden;
}

.map-modal-views-container {
    height: 100%;
    position: relative;
}

/* ===== TWO-LEVEL MODAL INTERFACE SYSTEM ===== */

/* Modal Views Container - Premium Animation System with Proper Scrolling */
.map-modal-view {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    will-change: transform, opacity;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--map-gray-300) transparent;
}

.map-modal-view-active {
    display: block;
    opacity: 1;
    transform: translateX(0);
    z-index: 2;
}

/* Scrollbar styling for modal views */
.map-modal-view::-webkit-scrollbar {
    width: 6px;
}

.map-modal-view::-webkit-scrollbar-track {
    background: transparent;
}

.map-modal-view::-webkit-scrollbar-thumb {
    background: var(--map-gray-300);
    border-radius: var(--map-radius-full);
}

.map-modal-view::-webkit-scrollbar-thumb:hover {
    background: var(--map-gray-400);
}

/* Premium Forward Navigation Animation (Category → Options) */
.map-modal-view.map-view-entering-forward {
    display: block;
    opacity: 0;
    transform: translateX(100%) scale(0.95);
    animation: slideInForward 0.45s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    z-index: 3;
}

.map-modal-view.map-view-exiting-forward {
    opacity: 1;
    transform: translateX(0) scale(1);
    animation: slideOutForward 0.45s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    z-index: 1;
}

/* Premium Backward Navigation Animation (Options → Category) */
.map-modal-view.map-view-entering-backward {
    display: block;
    opacity: 0;
    transform: translateX(-100%) scale(0.95);
    animation: slideInBackward 0.45s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    z-index: 3;
}

.map-modal-view.map-view-exiting-backward {
    opacity: 1;
    transform: translateX(0) scale(1);
    animation: slideOutBackward 0.45s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    z-index: 1;
}

/* Forward Animation Keyframes */
@keyframes slideInForward {
    0% {
        opacity: 0;
        transform: translateX(100%) scale(0.95);
        filter: blur(2px);
    }
    60% {
        opacity: 0.8;
        transform: translateX(10%) scale(0.98);
        filter: blur(1px);
    }
    100% {
        opacity: 1;
        transform: translateX(0) scale(1);
        filter: blur(0);
    }
}

@keyframes slideOutForward {
    0% {
        opacity: 1;
        transform: translateX(0) scale(1);
        filter: blur(0);
    }
    40% {
        opacity: 0.8;
        transform: translateX(-10%) scale(0.98);
        filter: blur(1px);
    }
    100% {
        opacity: 0;
        transform: translateX(-100%) scale(0.95);
        filter: blur(2px);
    }
}

/* Backward Animation Keyframes */
@keyframes slideInBackward {
    0% {
        opacity: 0;
        transform: translateX(-100%) scale(0.95);
        filter: blur(2px);
    }
    60% {
        opacity: 0.8;
        transform: translateX(-10%) scale(0.98);
        filter: blur(1px);
    }
    100% {
        opacity: 1;
        transform: translateX(0) scale(1);
        filter: blur(0);
    }
}

@keyframes slideOutBackward {
    0% {
        opacity: 1;
        transform: translateX(0) scale(1);
        filter: blur(0);
    }
    40% {
        opacity: 0.8;
        transform: translateX(10%) scale(0.98);
        filter: blur(1px);
    }
    100% {
        opacity: 0;
        transform: translateX(100%) scale(0.95);
        filter: blur(2px);
    }
}

/* View Content */
.map-view-content {
    padding: var(--map-space-4);
    height: 100%;
}

/* ===== MAIN MENU - CATEGORY GRID ===== */

.map-category-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--map-space-3);
    padding: var(--map-space-2);
}

/* Category Button */
.map-category-button {
    background: var(--map-white);
    border: 1px solid var(--map-gray-200);
    border-radius: var(--map-radius-lg);
    padding: var(--map-space-4);
    cursor: pointer;
    transition: all var(--map-transition-base);
    display: flex;
    align-items: center;
    gap: var(--map-space-4);
    text-align: left;
    width: 100%;
    position: relative;
    overflow: hidden;
}

.map-category-button:hover {
    background: var(--map-gray-50);
    border-color: var(--map-primary);
    transform: translateY(-2px);
    box-shadow: var(--map-shadow-md);
}

.map-category-button:active {
    transform: translateY(0);
}

.map-category-button:focus-visible {
    outline: 2px solid var(--map-primary);
    outline-offset: 2px;
}

/* Category Icon */
.map-category-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
    border-radius: var(--map-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--map-white);
    flex-shrink: 0;
    transition: all var(--map-transition-base);
}

.map-category-button:hover .map-category-icon {
    transform: scale(1.1) rotate(5deg);
}

/* Category Content */
.map-category-content {
    flex: 1;
    min-width: 0;
}

.map-category-title {
    font-size: var(--map-font-size-lg);
    font-weight: 600;
    color: var(--map-gray-900);
    margin-bottom: var(--map-space-1);
}

.map-category-desc {
    font-size: var(--map-font-size-sm);
    color: var(--map-gray-600);
    line-height: 1.4;
}

/* Category Arrow */
.map-category-arrow {
    color: var(--map-gray-400);
    transition: all var(--map-transition-base);
    flex-shrink: 0;
}

.map-category-button:hover .map-category-arrow {
    color: var(--map-primary);
    transform: translateX(4px);
}

/* ===== FEATURE SECTIONS ===== */

.map-feature-section {
    margin-bottom: var(--map-space-4);
}

/* Feature Toggle Button */
.map-feature-toggle {
    background: var(--map-white);
    border: 1px solid var(--map-gray-200);
    border-radius: var(--map-radius-lg);
    padding: var(--map-space-4);
    cursor: pointer;
    transition: all var(--map-transition-base);
    display: flex;
    align-items: center;
    gap: var(--map-space-4);
    text-align: left;
    width: 100%;
    position: relative;
    overflow: hidden;
}

.map-feature-toggle:hover {
    background: var(--map-gray-50);
    border-color: var(--map-primary);
    transform: translateY(-1px);
    box-shadow: var(--map-shadow-sm);
}

.map-feature-toggle:active {
    transform: translateY(0);
}

.map-feature-toggle:focus-visible {
    outline: 2px solid var(--map-primary);
    outline-offset: 2px;
}

/* Feature Icon */
.map-feature-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
    border-radius: var(--map-radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--map-white);
    flex-shrink: 0;
    transition: all var(--map-transition-base);
}

.map-feature-toggle:hover .map-feature-icon {
    transform: scale(1.05);
}

/* Feature Content */
.map-feature-content {
    flex: 1;
    min-width: 0;
}

.map-feature-title {
    font-size: var(--map-font-size-base);
    font-weight: 600;
    color: var(--map-gray-900);
    margin-bottom: var(--map-space-1);
}

.map-feature-desc {
    font-size: var(--map-font-size-sm);
    color: var(--map-gray-600);
    line-height: 1.4;
}

/* Feature Status */
.map-feature-status {
    font-size: var(--map-font-size-sm);
    color: var(--map-gray-500);
    font-weight: 500;
}

/* Toggle Switch */
.map-toggle-switch {
    width: 44px;
    height: 24px;
    background: var(--map-gray-300);
    border-radius: var(--map-radius-full);
    position: relative;
    transition: all var(--map-transition-base);
    flex-shrink: 0;
}

.map-toggle-slider {
    width: 20px;
    height: 20px;
    background: var(--map-white);
    border-radius: var(--map-radius-full);
    position: absolute;
    top: 2px;
    left: 2px;
    transition: all var(--map-transition-base);
    box-shadow: var(--map-shadow-sm);
}

/* Active Toggle State */
.map-feature-toggle[data-active="true"] .map-toggle-switch {
    background: var(--map-primary);
}

.map-feature-toggle[data-active="true"] .map-toggle-slider {
    transform: translateX(20px);
}

/* Feature Controls */
.map-feature-controls {
    margin-top: var(--map-space-3);
    padding: var(--map-space-4);
    background: var(--map-gray-50);
    border-radius: var(--map-radius-md);
    border: 1px solid var(--map-gray-200);
}

/* Status Messages */
.map-status-message {
    margin-top: var(--map-space-3);
    padding: var(--map-space-3);
    background: var(--map-gray-50);
    border-radius: var(--map-radius-md);
    border-left: 4px solid var(--map-primary);
    display: flex;
    align-items: center;
    gap: var(--map-space-2);
    font-size: var(--map-font-size-sm);
}

.map-status-icon {
    font-size: var(--map-font-size-lg);
}

.map-status-text {
    color: var(--map-gray-700);
    line-height: 1.4;
}

/* ===== FONT SIZE CONTROLS ===== */

.map-font-size-controls {
    display: flex;
    flex-direction: column;
    gap: var(--map-space-4);
}

.map-size-control-group {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--map-space-4);
}

.map-size-control-btn {
    width: 40px;
    height: 40px;
    background: var(--map-white);
    border: 2px solid var(--map-gray-300);
    border-radius: var(--map-radius-md);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--map-gray-600);
    transition: all var(--map-transition-base);
}

.map-size-control-btn:hover {
    background: var(--map-primary);
    border-color: var(--map-primary);
    color: var(--map-white);
    transform: scale(1.05);
}

.map-size-control-btn:active {
    transform: scale(0.95);
}

.map-size-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--map-space-1);
}

.map-size-preview {
    font-size: 24px;
    font-weight: 600;
    color: var(--map-gray-900);
    transition: all var(--map-transition-base);
}

.map-size-percentage {
    font-size: var(--map-font-size-sm);
    color: var(--map-gray-600);
    font-weight: 500;
}

.map-control-reset {
    background: var(--map-gray-100);
    border: 1px solid var(--map-gray-300);
    border-radius: var(--map-radius-md);
    padding: var(--map-space-2) var(--map-space-3);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--map-space-2);
    font-size: var(--map-font-size-sm);
    color: var(--map-gray-700);
    transition: all var(--map-transition-base);
    align-self: center;
}

.map-control-reset:hover {
    background: var(--map-gray-200);
    border-color: var(--map-gray-400);
}

/* ===== LINE SPACING CONTROLS ===== */

.map-line-spacing-controls {
    display: flex;
    flex-direction: column;
    gap: var(--map-space-4);
}

.map-spacing-control-group {
    display: flex;
    flex-direction: column;
    gap: var(--map-space-3);
}

.map-spacing-labels {
    display: flex;
    justify-content: space-between;
    font-size: var(--map-font-size-xs);
    color: var(--map-gray-500);
    font-weight: 500;
}

.map-slider-container {
    position: relative;
}

.map-premium-slider {
    width: 100%;
    height: 6px;
    background: var(--map-gray-200);
    border-radius: var(--map-radius-full);
    outline: none;
    cursor: pointer;
    -webkit-appearance: none;
    appearance: none;
}

.map-premium-slider::-webkit-slider-thumb {
    width: 20px;
    height: 20px;
    background: var(--map-primary);
    border-radius: var(--map-radius-full);
    cursor: pointer;
    -webkit-appearance: none;
    appearance: none;
    box-shadow: var(--map-shadow-sm);
    transition: all var(--map-transition-base);
}

.map-premium-slider::-webkit-slider-thumb:hover {
    transform: scale(1.1);
    box-shadow: var(--map-shadow-md);
}

.map-premium-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    background: var(--map-primary);
    border-radius: var(--map-radius-full);
    cursor: pointer;
    border: none;
    box-shadow: var(--map-shadow-sm);
    transition: all var(--map-transition-base);
}

.map-spacing-value {
    text-align: center;
    font-size: var(--map-font-size-sm);
    color: var(--map-gray-700);
    font-weight: 600;
}

/* ===== THEME SELECTOR ===== */

.map-theme-selector {
    display: flex;
    flex-direction: column;
    gap: var(--map-space-4);
    align-items: center;
}

.map-theme-preview-card {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--map-space-4);
    padding: var(--map-space-4);
    background: var(--map-gray-50);
    border-radius: var(--map-radius-lg);
    border: 1px solid var(--map-gray-200);
    width: 100%;
}

.map-theme-nav {
    background: var(--map-white);
    border: 1px solid var(--map-gray-300);
    border-radius: var(--map-radius-md);
    width: 36px;
    height: 36px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--map-gray-600);
    transition: all var(--map-transition-base);
}

.map-theme-nav:hover {
    background: var(--map-primary);
    border-color: var(--map-primary);
    color: var(--map-white);
    transform: scale(1.05);
}

.map-theme-icon-preview-container {
    flex: 1;
    display: flex;
    justify-content: center;
}

.map-theme-icon-preview {
    width: 60px;
    height: 60px;
    background: var(--map-white);
    border-radius: var(--map-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--map-primary);
    border: 2px solid var(--map-gray-200);
    transition: all var(--map-transition-base);
}

.map-theme-info {
    text-align: center;
}

.map-theme-title {
    font-size: var(--map-font-size-base);
    font-weight: 600;
    color: var(--map-gray-900);
    margin: 0;
}

.map-theme-dots {
    display: flex;
    gap: var(--map-space-2);
    justify-content: center;
}

.map-theme-dot {
    width: 8px;
    height: 8px;
    background: var(--map-gray-300);
    border-radius: var(--map-radius-full);
    border: none;
    cursor: pointer;
    transition: all var(--map-transition-base);
}

.map-theme-dot:hover {
    background: var(--map-gray-400);
    transform: scale(1.2);
}

.map-theme-dot.active {
    background: var(--map-primary);
    transform: scale(1.3);
}

/* ===== COLOR STUDIO ===== */

.map-color-studio-compact {
    display: flex;
    flex-direction: column;
    gap: var(--map-space-3);
}

.map-color-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--map-space-3);
    background: var(--map-white);
    border-radius: var(--map-radius-md);
    border: 1px solid var(--map-gray-200);
    transition: all var(--map-transition-base);
}

.map-color-row:hover {
    border-color: var(--map-primary);
    box-shadow: var(--map-shadow-sm);
}

.map-color-label {
    display: flex;
    align-items: center;
    gap: var(--map-space-3);
    flex: 1;
}

.map-color-icon {
    width: 32px;
    height: 32px;
    background: var(--map-gray-100);
    border-radius: var(--map-radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--map-gray-600);
}

.map-color-info {
    display: flex;
    flex-direction: column;
    gap: var(--map-space-1);
}

.map-color-title {
    font-size: var(--map-font-size-sm);
    font-weight: 600;
    color: var(--map-gray-900);
}

.map-color-desc {
    font-size: var(--map-font-size-xs);
    color: var(--map-gray-500);
}

.map-color-controls {
    display: flex;
    align-items: center;
    gap: var(--map-space-2);
}

.map-color-picker-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    gap: var(--map-space-2);
}

.map-color-picker-compact {
    width: 40px;
    height: 40px;
    border: 2px solid var(--map-gray-300);
    border-radius: var(--map-radius-md);
    cursor: pointer;
    background: none;
    transition: all var(--map-transition-base);
}

.map-color-picker-compact:hover {
    border-color: var(--map-primary);
    transform: scale(1.05);
}

.map-color-reset-btn {
    width: 24px;
    height: 24px;
    background: var(--map-gray-200);
    border: none;
    border-radius: var(--map-radius-sm);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--map-gray-600);
    transition: all var(--map-transition-base);
}

.map-color-reset-btn:hover {
    background: var(--map-error);
    color: var(--map-white);
    transform: scale(1.1);
}

/* ===== LANGUAGE OPTIONS ===== */

.map-language-options {
    display: flex;
    flex-direction: column;
    gap: var(--map-space-2);
}

.map-language-option {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--map-space-3);
    background: var(--map-white);
    border: 1px solid var(--map-gray-200);
    border-radius: var(--map-radius-md);
    cursor: pointer;
    transition: all var(--map-transition-base);
}

.map-language-option:hover {
    background: var(--map-gray-50);
    border-color: var(--map-primary);
    transform: translateY(-1px);
    box-shadow: var(--map-shadow-sm);
}

.map-language-option.active {
    background: var(--map-primary);
    border-color: var(--map-primary);
    color: var(--map-white);
}

.map-language-info {
    display: flex;
    align-items: center;
    gap: var(--map-space-3);
}

.map-language-flag {
    font-size: var(--map-font-size-lg);
    width: 32px;
    text-align: center;
}

.map-language-details {
    display: flex;
    flex-direction: column;
    gap: var(--map-space-1);
}

.map-language-name {
    font-size: var(--map-font-size-sm);
    font-weight: 600;
}

.map-language-native {
    font-size: var(--map-font-size-xs);
    opacity: 0.8;
}

.map-language-status {
    opacity: 0;
    transition: all var(--map-transition-base);
}

.map-language-option.active .map-language-status {
    opacity: 1;
}

.map-language-check {
    width: 20px;
    height: 20px;
    background: var(--map-white);
    border-radius: var(--map-radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--map-primary);
}

/* Position Grid for Menu Position */
.map-position-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--map-space-2);
}

/* ===== THEME APPLICATION STYLES ===== */

/* Dark Mode Theme */
body.map-theme-dark {
    background-color: #1a1a1a !important;
    color: #ffffff !important;
}

body.map-theme-dark * {
    background-color: inherit !important;
    color: inherit !important;
    border-color: #404040 !important;
}

body.map-theme-dark a {
    color: #60a5fa !important;
}

/* High Contrast Theme */
body.map-theme-high-contrast {
    background-color: #000000 !important;
    color: #ffffff !important;
}

body.map-theme-high-contrast * {
    background-color: #000000 !important;
    color: #ffffff !important;
    border-color: #ffffff !important;
}

body.map-theme-high-contrast a {
    color: #ffff00 !important;
}

/* Monochrome Theme */
body.map-theme-monochrome {
    filter: grayscale(100%) !important;
}

/* Low Saturation Theme */
body.map-theme-low-saturation {
    filter: saturate(0.5) !important;
}

/* High Saturation Theme */
body.map-theme-high-saturation {
    filter: saturate(1.5) !important;
}

/* Sepia Theme */
body.map-theme-sepia {
    filter: sepia(100%) !important;
}

/* Color Blind Friendly Theme */
body.map-theme-colorblind {
    background-color: #ffffff !important;
    color: #000000 !important;
}

body.map-theme-colorblind * {
    background-color: inherit !important;
    color: inherit !important;
    border-color: #000000 !important;
}

body.map-theme-colorblind a {
    color: #0066cc !important;
}

/* Custom Theme Applied State */
body.map-theme-custom {
    /* Custom theme styles will be applied dynamically via JavaScript */
}

/* ===== DYSLEXIC FONT APPLICATION ===== */

body.map-dyslexic-font-active,
body.map-dyslexic-font-active * {
    font-family: 'OpenDyslexic', 'Comic Sans MS', cursive !important;
}

/* ===== FONT SIZE ADJUSTMENTS ===== */

body.map-font-size-adjusted {
    /* Font size will be applied dynamically via CSS custom properties */
}

/* ===== LINE SPACING ADJUSTMENTS ===== */

body.map-line-spacing-adjusted,
body.map-line-spacing-adjusted * {
    /* Line spacing will be applied dynamically via CSS custom properties */
}

/* ===== READING GUIDE ===== */

#map-reading-guide {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--map-primary);
    z-index: var(--map-z-tooltip);
    pointer-events: none;
    opacity: 0.8;
    box-shadow: 0 0 10px rgba(99, 102, 241, 0.5);
    transition: all 0.1s ease-out;
}

/* ===== ADHD FOCUS MODE ===== */

#map-adhd-focus-border {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 4px solid var(--map-primary);
    pointer-events: none;
    z-index: var(--map-z-modal);
    box-shadow:
        0 0 20px rgba(99, 102, 241, 0.5),
        inset 0 0 20px rgba(99, 102, 241, 0.2);
    animation: focusPulse 3s ease-in-out infinite;
}

@keyframes focusPulse {
    0%, 100% {
        opacity: 0.8;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.002);
    }
}

/* ===== BIG CURSOR ===== */

body.map-big-cursor-active,
body.map-big-cursor-active * {
    cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32"><path fill="black" d="M2 2l12 10-4 4 8 8-4 4-8-8-4 4z"/></svg>') 2 2, auto !important;
}

/* ===== TEXT MAGNIFICATION ===== */

.map-text-magnifier {
    position: fixed;
    width: 200px;
    height: 100px;
    background: var(--map-white);
    border: 2px solid var(--map-primary);
    border-radius: var(--map-radius-md);
    padding: var(--map-space-2);
    font-size: 18px;
    line-height: 1.4;
    z-index: var(--map-z-tooltip);
    pointer-events: none;
    box-shadow: var(--map-shadow-xl);
    transform: translate(-50%, -120%);
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 480px) {
    .map-widget-panel {
        width: 320px;
        height: 450px;
    }

    .map-category-grid {
        gap: var(--map-space-2);
    }

    .map-category-button {
        padding: var(--map-space-3);
    }

    .map-category-icon {
        width: 40px;
        height: 40px;
    }

    .map-feature-toggle {
        padding: var(--map-space-3);
    }

    .map-feature-icon {
        width: 36px;
        height: 36px;
    }
}
