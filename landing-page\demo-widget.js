/**
 * Standalone Accessibility Widget Demo
 * Basic functionality to make the widget interactive
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // ===== WIDGET TOGGLE FUNCTIONALITY =====
    const mainToggle = document.getElementById('map-main-toggle');
    const widgetPanel = document.getElementById('map-widget-panel');
    const closeButton = document.getElementById('map-close-panel');
    
    if (mainToggle && widgetPanel) {
        // Open widget panel
        mainToggle.addEventListener('click', function() {
            const isOpen = widgetPanel.style.display !== 'none';
            
            if (isOpen) {
                // Close panel
                widgetPanel.style.display = 'none';
                widgetPanel.setAttribute('aria-hidden', 'true');
                mainToggle.setAttribute('aria-expanded', 'false');
                document.body.classList.remove('map-panel-open');
            } else {
                // Open panel
                widgetPanel.style.display = 'block';
                widgetPanel.setAttribute('aria-hidden', 'false');
                mainToggle.setAttribute('aria-expanded', 'true');
                document.body.classList.add('map-panel-open');
            }
        });
        
        // Close button functionality
        if (closeButton) {
            closeButton.addEventListener('click', function() {
                widgetPanel.style.display = 'none';
                widgetPanel.setAttribute('aria-hidden', 'true');
                mainToggle.setAttribute('aria-expanded', 'false');
                document.body.classList.remove('map-panel-open');
            });
        }
    }
    
    // ===== CATEGORY NAVIGATION =====
    const categoryButtons = document.querySelectorAll('.map-category-button');
    const backButton = document.getElementById('map-header-back-button');
    const mainMenu = document.getElementById('map-main-menu');
    const panelTitle = document.getElementById('map-panel-title');
    const headerNavigation = document.getElementById('map-header-navigation');
    const categoryTitle = document.getElementById('map-header-category-title');
    
    categoryButtons.forEach(button => {
        button.addEventListener('click', function() {
            const category = this.dataset.category;
            const categoryName = this.querySelector('.map-category-title').textContent;
            
            // Show navigation header
            if (panelTitle) panelTitle.style.display = 'none';
            if (headerNavigation) headerNavigation.style.display = 'flex';
            if (categoryTitle) categoryTitle.textContent = categoryName;
            
            // For now, just show a message that the category was selected
            console.log(`Selected category: ${category}`);
            
            // Add visual feedback
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    });
    
    // Back button functionality
    if (backButton) {
        backButton.addEventListener('click', function() {
            // Show main title, hide navigation
            if (panelTitle) panelTitle.style.display = 'block';
            if (headerNavigation) headerNavigation.style.display = 'none';
        });
    }
    
    // ===== DEMO NOTIFICATIONS =====
    function showDemoNotification(message) {
        // Create a simple notification
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #6366f1;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            max-width: 300px;
            animation: slideInRight 0.3s ease-out;
        `;
        notification.textContent = message;
        
        // Add animation keyframes
        if (!document.getElementById('demo-notification-styles')) {
            const styles = document.createElement('style');
            styles.id = 'demo-notification-styles';
            styles.textContent = `
                @keyframes slideInRight {
                    from {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(0);
                        opacity: 1;
                    }
                }
                @keyframes slideOutRight {
                    from {
                        transform: translateX(0);
                        opacity: 1;
                    }
                    to {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(styles);
        }
        
        document.body.appendChild(notification);
        
        // Auto remove after 3 seconds
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease-out';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
    
    // ===== WELCOME MESSAGE =====
    setTimeout(() => {
        showDemoNotification('🎯 Accessibility Widget Demo is ready! Click the button to explore features.');
    }, 1000);
    
    // ===== KEYBOARD NAVIGATION =====
    document.addEventListener('keydown', function(e) {
        // ESC key closes the panel
        if (e.key === 'Escape' && widgetPanel && widgetPanel.style.display !== 'none') {
            closeButton.click();
        }
        
        // Alt + A opens the accessibility panel
        if (e.altKey && e.key === 'a') {
            e.preventDefault();
            if (mainToggle) {
                mainToggle.click();
            }
        }
    });
    
    // ===== CLICK OUTSIDE TO CLOSE =====
    document.addEventListener('click', function(e) {
        if (widgetPanel && widgetPanel.style.display !== 'none') {
            const widget = document.getElementById('map-accessibility-widget');
            if (widget && !widget.contains(e.target)) {
                closeButton.click();
            }
        }
    });
    
    // ===== DEMO FEATURE PLACEHOLDERS =====
    // These will be replaced with full functionality in Phase 3
    
    // Add click handlers for demo purposes
    document.addEventListener('click', function(e) {
        if (e.target.closest('.map-category-button')) {
            const category = e.target.closest('.map-category-button').dataset.category;
            showDemoNotification(`📋 ${category.charAt(0).toUpperCase() + category.slice(1)} features coming in Phase 3!`);
        }
    });
    
    console.log('🎯 Accessibility Widget Demo loaded successfully!');
    console.log('💡 Keyboard shortcuts:');
    console.log('   - Alt + A: Open/close accessibility panel');
    console.log('   - ESC: Close panel');
});
