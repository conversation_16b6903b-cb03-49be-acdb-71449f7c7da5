/**
 * Standalone Accessibility Widget CSS
 * Extracted and adapted from WordPress plugin for demo purposes
 */

/* ===== CSS VARIABLES - MATCHING PLUGIN DESIGN ===== */
:root {
    /* Primary Brand Colors - From Plugin */
    --map-primary: #6366f1;
    --map-primary-light: #818cf8;
    --map-primary-dark: #4f46e5;
    --map-accent: #f59e0b;
    --map-accent-light: #fbbf24;

    /* Neutral Palette */
    --map-white: #ffffff;
    --map-gray-50: #f9fafb;
    --map-gray-100: #f3f4f6;
    --map-gray-200: #e5e7eb;
    --map-gray-300: #d1d5db;
    --map-gray-400: #9ca3af;
    --map-gray-500: #6b7280;
    --map-gray-600: #4b5563;
    --map-gray-700: #374151;
    --map-gray-800: #1f2937;
    --map-gray-900: #111827;

    /* Semantic Colors */
    --map-success: #059669;
    --map-warning: #d97706;
    --map-error: #dc2626;
    --map-info: #0284c7;

    /* Theme Semantic Variables */
    --map-surface: var(--map-white);
    --map-border: var(--map-gray-300);
    --map-text: var(--map-gray-700);
    --map-text-secondary: var(--map-gray-500);
    --map-bg-rgb: 255, 255, 255;

    /* Design Tokens */
    --map-radius-sm: 6px;
    --map-radius-md: 12px;
    --map-radius-lg: 16px;
    --map-radius-xl: 24px;
    --map-radius-full: 9999px;

    /* Shadows */
    --map-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --map-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --map-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --map-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Spacing */
    --map-space-1: 0.25rem;
    --map-space-2: 0.5rem;
    --map-space-3: 0.75rem;
    --map-space-4: 1rem;
    --map-space-5: 1.25rem;
    --map-space-6: 1.5rem;
    --map-space-8: 2rem;
    --map-space-10: 2.5rem;
    --map-space-12: 3rem;
    --map-space-16: 4rem;
    --map-space-20: 5rem;

    /* Transitions */
    --map-transition-base: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --map-transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== ACCESSIBILITY WIDGET CONTAINER ===== */
.map-accessibility-widget {
    position: fixed;
    z-index: 999999;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: var(--map-text);
    direction: ltr;
    text-align: left;
}

/* Position Classes */
.map-position-bottom-right {
    bottom: var(--map-space-6);
    right: var(--map-space-6);
}

.map-position-bottom-left {
    bottom: var(--map-space-6);
    left: var(--map-space-6);
}

.map-position-top-right {
    top: var(--map-space-6);
    right: var(--map-space-6);
}

.map-position-top-left {
    top: var(--map-space-6);
    left: var(--map-space-6);
}

/* ===== MAIN TOGGLE BUTTON ===== */
.map-main-toggle {
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-dark) 100%);
    color: var(--map-white);
    border: none;
    border-radius: var(--map-radius-full);
    width: 64px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: var(--map-shadow-lg);
    transition: var(--map-transition-base);
    position: relative;
    overflow: hidden;
}

.map-main-toggle:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: var(--map-shadow-xl);
    background: linear-gradient(135deg, var(--map-primary-light) 0%, var(--map-primary) 100%);
}

.map-main-toggle:active {
    transform: translateY(0) scale(0.98);
}

.map-main-toggle:focus-visible {
    outline: 3px solid rgba(99, 102, 241, 0.3);
    outline-offset: 2px;
}

.map-toggle-icon {
    transition: var(--map-transition-base);
}

.map-panel-open .map-toggle-icon {
    transform: rotate(180deg);
}

/* ===== WIDGET PANEL ===== */
.map-widget-panel {
    position: absolute;
    bottom: 80px;
    right: 0;
    width: 380px;
    max-height: 600px;
    background: var(--map-white);
    border-radius: var(--map-radius-xl);
    box-shadow: var(--map-shadow-xl);
    border: 1px solid var(--map-border);
    overflow: hidden;
    transform-origin: bottom right;
    animation: mapPanelSlideIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes mapPanelSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Responsive panel positioning */
.map-position-bottom-left .map-widget-panel {
    bottom: 80px;
    left: 0;
    transform-origin: bottom left;
}

.map-position-top-right .map-widget-panel {
    top: 80px;
    right: 0;
    bottom: auto;
    transform-origin: top right;
}

.map-position-top-left .map-widget-panel {
    top: 80px;
    left: 0;
    bottom: auto;
    transform-origin: top left;
}

/* ===== PANEL HEADER ===== */
.map-panel-header {
    background: linear-gradient(135deg, var(--map-gray-50) 0%, var(--map-white) 100%);
    padding: var(--map-space-4) var(--map-space-5);
    border-bottom: 1px solid var(--map-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
}

.map-panel-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--map-gray-900);
    margin: 0;
}

.map-header-navigation {
    display: flex;
    align-items: center;
    gap: var(--map-space-3);
}

.map-header-back-button {
    background: none;
    border: none;
    color: var(--map-gray-600);
    cursor: pointer;
    padding: var(--map-space-2);
    border-radius: var(--map-radius-md);
    transition: var(--map-transition-base);
    display: flex;
    align-items: center;
    justify-content: center;
}

.map-header-back-button:hover {
    background: var(--map-gray-100);
    color: var(--map-primary);
}

.map-header-category-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--map-gray-900);
    margin: 0;
}

.map-header-buttons {
    display: flex;
    align-items: center;
    gap: var(--map-space-2);
}

.map-close-button {
    background: none;
    border: none;
    color: var(--map-gray-500);
    cursor: pointer;
    padding: var(--map-space-2);
    border-radius: var(--map-radius-md);
    transition: var(--map-transition-base);
    display: flex;
    align-items: center;
    justify-content: center;
}

.map-close-button:hover {
    background: var(--map-gray-100);
    color: var(--map-error);
}

/* ===== PANEL CONTENT ===== */
.map-panel-content {
    position: relative;
    height: 500px;
    overflow: hidden;
}

.map-modal-views-container {
    position: relative;
    width: 100%;
    height: 100%;
}

/* ===== MODAL VIEW SYSTEM ===== */
.map-modal-view {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--map-gray-300) transparent;
}

.map-modal-view-active {
    display: block;
    opacity: 1;
    transform: translateX(0);
    z-index: 2;
}

/* Scrollbar styling */
.map-modal-view::-webkit-scrollbar {
    width: 6px;
}

.map-modal-view::-webkit-scrollbar-track {
    background: transparent;
}

.map-modal-view::-webkit-scrollbar-thumb {
    background: var(--map-gray-300);
    border-radius: var(--map-radius-full);
}

.map-modal-view::-webkit-scrollbar-thumb:hover {
    background: var(--map-gray-400);
}

/* ===== VIEW CONTENT ===== */
.map-view-content {
    padding: var(--map-space-5);
}

/* ===== CATEGORY GRID (MAIN MENU) ===== */
.map-category-grid {
    display: flex;
    flex-direction: column;
    gap: var(--map-space-3);
}

.map-category-button {
    background: var(--map-white);
    border: 1px solid var(--map-border);
    border-radius: var(--map-radius-lg);
    padding: var(--map-space-4);
    cursor: pointer;
    transition: var(--map-transition-base);
    display: flex;
    align-items: center;
    gap: var(--map-space-4);
    text-align: left;
    width: 100%;
    position: relative;
    overflow: hidden;
}

.map-category-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--map-primary), var(--map-accent));
    opacity: 0;
    transition: var(--map-transition-base);
}

.map-category-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--map-shadow-lg);
    border-color: var(--map-primary);
}

.map-category-button:hover::before {
    opacity: 1;
}

.map-category-button:focus-visible {
    outline: 2px solid var(--map-primary);
    outline-offset: 2px;
}

.map-category-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
    border-radius: var(--map-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--map-white);
    flex-shrink: 0;
    transition: var(--map-transition-base);
}

.map-category-button:hover .map-category-icon {
    transform: scale(1.1) rotate(5deg);
}

.map-category-content {
    flex: 1;
    min-width: 0;
}

.map-category-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--map-gray-900);
    margin-bottom: var(--map-space-1);
}

.map-category-desc {
    font-size: 13px;
    color: var(--map-gray-500);
    line-height: 1.4;
}

.map-category-arrow {
    color: var(--map-gray-400);
    transition: var(--map-transition-base);
    flex-shrink: 0;
}

.map-category-button:hover .map-category-arrow {
    color: var(--map-primary);
    transform: translateX(4px);
}

/* ===== FEATURE SECTIONS ===== */
.map-feature-section {
    margin-bottom: var(--map-space-4);
}

.map-feature-toggle {
    background: var(--map-white);
    border: 1px solid var(--map-border);
    border-radius: var(--map-radius-lg);
    padding: var(--map-space-4);
    cursor: pointer;
    transition: var(--map-transition-base);
    display: flex;
    align-items: center;
    gap: var(--map-space-4);
    text-align: left;
    width: 100%;
    position: relative;
}

.map-feature-toggle:hover {
    background: var(--map-gray-50);
    border-color: var(--map-primary);
}

.map-feature-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
    border-radius: var(--map-radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--map-white);
    flex-shrink: 0;
}

.map-feature-content {
    flex: 1;
    min-width: 0;
}

.map-feature-title {
    font-size: 15px;
    font-weight: 600;
    color: var(--map-gray-900);
    margin-bottom: var(--map-space-1);
}

.map-feature-desc {
    font-size: 13px;
    color: var(--map-gray-500);
    line-height: 1.4;
}

/* ===== TOGGLE SWITCHES ===== */
.map-toggle-switch {
    position: relative;
    width: 44px;
    height: 24px;
    background: var(--map-gray-300);
    border-radius: var(--map-radius-full);
    transition: var(--map-transition-base);
    flex-shrink: 0;
}

.map-toggle-slider {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: var(--map-white);
    border-radius: var(--map-radius-full);
    transition: var(--map-transition-base);
    box-shadow: var(--map-shadow-sm);
}

.map-feature-toggle[data-active="true"] .map-toggle-switch {
    background: var(--map-primary);
}

.map-feature-toggle[data-active="true"] .map-toggle-slider {
    transform: translateX(20px);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 480px) {
    .map-widget-panel {
        width: calc(100vw - 32px);
        max-width: 360px;
        left: 50% !important;
        right: auto !important;
        transform: translateX(-50%);
    }

    .map-position-bottom-right .map-widget-panel,
    .map-position-bottom-left .map-widget-panel {
        bottom: 80px;
    }

    .map-position-top-right .map-widget-panel,
    .map-position-top-left .map-widget-panel {
        top: 80px;
        bottom: auto;
    }

    .map-category-button,
    .map-feature-toggle {
        padding: var(--map-space-3);
        gap: var(--map-space-3);
    }

    .map-category-icon {
        width: 40px;
        height: 40px;
    }

    .map-feature-icon {
        width: 36px;
        height: 36px;
    }
}
