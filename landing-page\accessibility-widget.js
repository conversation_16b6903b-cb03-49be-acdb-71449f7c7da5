/**
 * Standalone Accessibility Widget JavaScript
 * Using original plugin structure with j<PERSON><PERSON>y for perfect compatibility
 */

// Mock mapAjax object for standalone demo
window.mapAjax = {
    plugin_url: './landing-page/',
    settings: {
        speech_rate: 1.0,
        speech_pitch: 1.0,
        speech_volume: 1.0,
        widget_position: 'bottom-right'
    },
    strings: {
        play: 'Play',
        pause: 'Pause',
        stop: 'Stop',
        loading: 'Loading...',
        error: 'Speech synthesis not supported in this browser.'
    }
};

// Include jQuery if not already loaded
if (typeof jQuery === 'undefined') {
    const script = document.createElement('script');
    script.src = 'https://code.jquery.com/jquery-3.6.0.min.js';
    script.onload = initializeStandaloneWidget;
    document.head.appendChild(script);
} else {
    initializeStandaloneWidget();
}

function initializeStandaloneWidget() {
    const $ = jQuery;

    // Create the widget HTML structure using the exact same structure as the original plugin
    function createWidget() {
        const widgetHTML = `
            <div id="map-accessibility-widget" class="map-accessibility-widget map-position-bottom-right" role="region" aria-label="Accessibility Tools">

                <!-- Main Toggle Button -->
                <button id="map-main-toggle" class="map-main-toggle" type="button" aria-expanded="false" aria-controls="map-widget-panel" aria-label="Open accessibility tools">
                    <span class="map-toggle-icon" aria-hidden="true">
                        <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                    </span>
                </button>

                <!-- Widget Panel -->
                <div id="map-widget-panel" class="map-widget-panel" style="display: none;" aria-hidden="true">
                    <div class="map-panel-header">
                        <!-- Main title - shown when on main menu -->
                        <h3 id="map-panel-title" class="map-panel-title">Accessibility Tools</h3>

                        <!-- Back button with category title - shown when in category views -->
                        <div id="map-header-navigation" class="map-header-navigation" style="display: none;">
                            <button id="map-header-back-button" class="map-header-back-button" type="button" aria-label="Back to main menu">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M15.41 16.59L10.83 12l4.58-4.59L14 6l-6 6 6 6 1.41-1.41z"/>
                                </svg>
                            </button>
                            <h4 id="map-header-category-title" class="map-header-category-title"></h4>
                        </div>

                        <div class="map-header-buttons">
                            <button id="map-close-panel" class="map-close-button" type="button" aria-label="Close accessibility tools">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                                </svg>
                            </button>
                        </div>
                    </div>

                    <div class="map-panel-content">
                        <!-- Premium Animation Container for Modal Views -->
                        <div class="map-modal-views-container">
                            <!-- Main Menu (Level 1) - Category Selection -->
                            <div id="map-main-menu" class="map-modal-view map-modal-view-active" role="menu" aria-label="Accessibility categories">
                                <div class="map-category-grid">
                                    <!-- Text Category -->
                                    <button id="map-category-text" class="map-category-button" type="button" role="menuitem" data-category="text" aria-describedby="map-category-text-desc">
                                        <div class="map-category-icon map-category-icon-text">
                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                                <polyline points="14,2 14,8 20,8"/>
                                                <line x1="16" y1="13" x2="8" y2="13"/>
                                                <line x1="16" y1="17" x2="8" y2="17"/>
                                                <polyline points="10,9 9,9 8,9"/>
                                            </svg>
                                        </div>
                                        <div class="map-category-content">
                                            <div class="map-category-title">Text</div>
                                            <div id="map-category-text-desc" class="map-category-desc">Reading and text options</div>
                                        </div>
                                        <div class="map-category-arrow" aria-hidden="true">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                                <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                                            </svg>
                                        </div>
                                    </button>

                                    <!-- Colors Category -->
                                    <button id="map-category-colors" class="map-category-button" type="button" role="menuitem" data-category="colors" aria-describedby="map-category-colors-desc">
                                        <div class="map-category-icon map-category-icon-colors">
                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <circle cx="13.5" cy="6.5" r=".5"/>
                                                <circle cx="17.5" cy="10.5" r=".5"/>
                                                <circle cx="8.5" cy="7.5" r=".5"/>
                                                <circle cx="6.5" cy="12.5" r=".5"/>
                                                <path d="M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z"/>
                                            </svg>
                                        </div>
                                        <div class="map-category-content">
                                            <div class="map-category-title">Contrast & Colors</div>
                                            <div id="map-category-colors-desc" class="map-category-desc">Contrast and color themes</div>
                                        </div>
                                        <div class="map-category-arrow" aria-hidden="true">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                                <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                                            </svg>
                                        </div>
                                    </button>

                                    <!-- Navigation Category -->
                                    <button id="map-category-navigation" class="map-category-button" type="button" role="menuitem" data-category="navigation" aria-describedby="map-category-navigation-desc">
                                        <div class="map-category-icon map-category-icon-navigation">
                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <rect x="2" y="4" width="20" height="16" rx="2"/>
                                                <path d="M6 8h.01"/>
                                                <path d="M10 8h.01"/>
                                                <path d="M14 8h.01"/>
                                                <path d="M18 8h.01"/>
                                                <path d="M8 12h.01"/>
                                                <path d="M12 12h.01"/>
                                                <path d="M16 12h.01"/>
                                                <path d="M7 16h10"/>
                                            </svg>
                                        </div>
                                        <div class="map-category-content">
                                            <div class="map-category-title">Navigation</div>
                                            <div id="map-category-navigation-desc" class="map-category-desc">Keyboard shortcuts and navigation aids</div>
                                        </div>
                                        <div class="map-category-arrow" aria-hidden="true">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                                <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                                            </svg>
                                        </div>
                                    </button>

                                    <!-- Preferences Category -->
                                    <button id="map-category-preferences" class="map-category-button" type="button" role="menuitem" data-category="preferences" aria-describedby="map-category-preferences-desc">
                                        <div class="map-category-icon map-category-icon-preferences">
                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <circle cx="12" cy="12" r="3"/>
                                                <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                                            </svg>
                                        </div>
                                        <div class="map-category-content">
                                            <div class="map-category-title">Preferences</div>
                                            <div id="map-category-preferences-desc" class="map-category-desc">General settings and preferences</div>
                                        </div>
                                        <div class="map-category-arrow" aria-hidden="true">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                                <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                                            </svg>
                                        </div>
                                    </button>
                                </div>
                            </div>

                            <!-- Placeholder views for other categories (Phase 1) -->
                            <div id="map-view-text" class="map-modal-view" role="region" aria-label="Text accessibility options" data-title="Text Options">
                                <div class="map-view-content">
                                    <p style="text-align: center; padding: 2rem; color: #6b7280; font-size: 16px;">
                                        📝 Text features will be implemented in Phase 2<br><br>
                                        This will include:<br>
                                        • Text-to-Speech<br>
                                        • Dyslexic Font<br>
                                        • Reading Guide<br>
                                        • Font Size Controls<br>
                                        • Line Spacing
                                    </p>
                                </div>
                            </div>

                            <div id="map-view-colors" class="map-modal-view" role="region" aria-label="Color accessibility options" data-title="Contrast & Colors">
                                <div class="map-view-content">
                                    <p style="text-align: center; padding: 2rem; color: #6b7280; font-size: 16px;">
                                        🎨 Color features will be implemented in Phase 3<br><br>
                                        This will include:<br>
                                        • Visual Themes<br>
                                        • Color Studio<br>
                                        • High Contrast Modes<br>
                                        • Custom Color Picker
                                    </p>
                                </div>
                            </div>

                            <div id="map-view-navigation" class="map-modal-view" role="region" aria-label="Navigation accessibility options" data-title="Navigation Options">
                                <div class="map-view-content">
                                    <p style="text-align: center; padding: 2rem; color: #6b7280; font-size: 16px;">
                                        🧭 Navigation features will be implemented in Phase 4<br><br>
                                        This will include:<br>
                                        • ADHD Focus Mode<br>
                                        • Big Cursor<br>
                                        • Text Magnification<br>
                                        • Keyboard Navigation
                                    </p>
                                </div>
                            </div>

                            <div id="map-view-preferences" class="map-modal-view" role="region" aria-label="General preferences" data-title="Preferences">
                                <div class="map-view-content">
                                    <p style="text-align: center; padding: 2rem; color: #6b7280; font-size: 16px;">
                                        ⚙️ Preferences will be implemented in Phase 5<br><br>
                                        This will include:<br>
                                        • Dark Mode Toggle<br>
                                        • Menu Position<br>
                                        • Language Selection<br>
                                        • Settings Export/Import
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Insert widget into demo container
        const demoContainer = document.getElementById('demo-container');
        if (demoContainer) {
            demoContainer.insertAdjacentHTML('beforeend', widgetHTML);
        }
    }
    
    // Simplified navigation logic using the original plugin's approach
    let currentView = 'main-menu';
    let isTransitioning = false;

    // Event handlers using jQuery delegation (like the original plugin)
    function bindEvents() {
        // Main toggle button
        $(document).on('click.mapAccessibility', '#map-main-toggle', function(e) {
            e.preventDefault();
            toggleWidget();
        });

        // Close panel button
        $(document).on('click.mapAccessibility', '#map-close-panel', function(e) {
            e.preventDefault();
            closeWidget();
        });

        // Category buttons
        $(document).on('click.mapAccessibility', '.map-category-button:not(.map-category-disabled)', function(e) {
            e.preventDefault();
            const category = $(this).data('category');
            if (category) {
                showSection(category);
            }
        });

        // Back buttons (including header back button)
        $(document).on('click.mapAccessibility', '.map-back-button, #map-header-back-button', function(e) {
            e.preventDefault();
            showMainMenu();
        });

        // Keyboard shortcuts
        $(document).on('keydown', function(e) {
            if (e.altKey && e.key === 'a') {
                e.preventDefault();
                toggleWidget();
            }
            if (e.key === 'Escape') {
                closeWidget();
            }
        });
    }

    function toggleWidget() {
        const $panel = $('#map-widget-panel');
        if ($panel.is(':visible')) {
            closeWidget();
        } else {
            openWidget();
        }
    }

    function openWidget() {
        const $toggle = $('#map-main-toggle');
        const $panel = $('#map-widget-panel');
        const $widget = $('#map-accessibility-widget');

        $toggle.attr('aria-expanded', 'true');
        $panel.show().attr('aria-hidden', 'false');
        $widget.addClass('map-panel-active');
        $toggle.addClass('map-panel-open');

        // Start button fade immediately (like the original plugin)
        setTimeout(() => {
            $toggle.removeClass('map-panel-open').addClass('map-fading-out');
        }, 100); // Very brief delay to show button over panel

        // Ensure main menu is visible
        showMainMenu();
    }

    function closeWidget() {
        const $toggle = $('#map-main-toggle');
        const $panel = $('#map-widget-panel');
        const $widget = $('#map-accessibility-widget');

        // Immediately restore button visibility (like the original plugin)
        $toggle.removeClass('map-fading-out map-panel-open');
        $widget.removeClass('map-panel-active');

        // Hide panel and update ARIA
        $toggle.attr('aria-expanded', 'false');
        $panel.hide().attr('aria-hidden', 'true');

        // Reset to main menu
        showMainMenu();
    }

    function showMainMenu() {
        currentView = 'main-menu';

        // Update header: show title, hide navigation
        $('#map-panel-title').show();
        $('#map-header-navigation').hide();

        // Switch view
        switchView('map-main-menu');
    }

    function showSection(section) {
        currentView = section;

        // Update header: hide title, show navigation
        $('#map-panel-title').hide();
        $('#map-header-navigation').show();

        // Set category title
        const titles = {
            text: 'Text Options',
            colors: 'Contrast & Colors',
            navigation: 'Navigation Options',
            preferences: 'Preferences'
        };
        $('#map-header-category-title').text(titles[section] || section);

        // Switch view
        switchView(`map-view-${section}`);
    }

    function switchView(targetViewId) {
        if (isTransitioning) return;

        isTransitioning = true;
        const $currentView = $('.map-modal-view-active');
        const $targetView = $(`#${targetViewId}`);

        if ($targetView.length === 0) {
            isTransitioning = false;
            return;
        }

        // Hide current view
        if ($currentView.length > 0) {
            $currentView.removeClass('map-modal-view-active').hide();
        }

        // Show target view with smooth transition
        $targetView.show();
        setTimeout(() => {
            $targetView.addClass('map-modal-view-active');
            isTransitioning = false;
        }, 50);
    }
    
    // Initialize everything
    createWidget();
    bindEvents();

    // Expose functions globally for the floating demo button
    window.accessibilityWidget = {
        openWidget: openWidget,
        closeWidget: closeWidget,
        toggleWidget: toggleWidget
    };
}

// Initialize when DOM is ready
$(document).ready(function() {
    // Only initialize if we're on the landing page and demo container exists
    if ($('#demo-container').length) {
        initializeStandaloneWidget();
    }
});
