/**
 * Standalone Accessibility Widget JavaScript
 * Extracted and adapted from WordPress plugin for demo purposes
 */

class StandaloneAccessibilityWidget {
    constructor() {
        this.currentView = 'main-menu';
        this.currentCategory = null;
        this.isTransitioning = false;
        this.previousView = null;
        
        // Feature states
        this.features = {
            textToSpeech: false,
            dyslexicFont: false,
            readingGuide: false,
            fontSize: 100,
            lineSpacing: 1.5,
            adhdFocus: false,
            bigCursor: false,
            textMagnification: false,
            darkMode: false,
            position: 'bottom-right',
            language: 'en'
        };
        
        // Load saved preferences
        this.loadPreferences();
        
        // Initialize widget
        this.init();
    }
    
    /**
     * Initialize the widget
     */
    init() {
        this.createWidget();
        this.bindEvents();
        this.applyPosition();
    }
    
    /**
     * Create the widget HTML structure
     */
    createWidget() {
        const widgetHTML = `
            <div id="map-accessibility-widget" class="map-accessibility-widget map-position-${this.features.position}" role="region" aria-label="Accessibility Tools">
                <!-- Main Toggle Button -->
                <button id="map-main-toggle" class="map-main-toggle" type="button" aria-expanded="false" aria-controls="map-widget-panel" aria-label="Open accessibility tools">
                    <span class="map-toggle-icon" aria-hidden="true">
                        <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                    </span>
                </button>
                
                <!-- Widget Panel -->
                <div id="map-widget-panel" class="map-widget-panel" style="display: none;" aria-hidden="true">
                    <div class="map-panel-header">
                        <!-- Main title - shown when on main menu -->
                        <h3 id="map-panel-title" class="map-panel-title">Accessibility Tools</h3>
                        
                        <!-- Back button with category title - shown when in category views -->
                        <div id="map-header-navigation" class="map-header-navigation" style="display: none;">
                            <button id="map-header-back-button" class="map-header-back-button" type="button" aria-label="Back to main menu">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M15.41 16.59L10.83 12l4.58-4.59L14 6l-6 6 6 6 1.41-1.41z"/>
                                </svg>
                            </button>
                            <h4 id="map-header-category-title" class="map-header-category-title"></h4>
                        </div>
                        
                        <div class="map-header-buttons">
                            <button id="map-close-panel" class="map-close-button" type="button" aria-label="Close accessibility tools">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                    
                    <div class="map-panel-content">
                        <div class="map-modal-views-container">
                            ${this.createMainMenuHTML()}
                            ${this.createTextViewHTML()}
                            ${this.createColorsViewHTML()}
                            ${this.createNavigationViewHTML()}
                            ${this.createPreferencesViewHTML()}
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Insert widget into demo container
        const demoContainer = document.getElementById('demo-container');
        if (demoContainer) {
            demoContainer.insertAdjacentHTML('beforeend', widgetHTML);
        }
    }
    
    /**
     * Create main menu HTML
     */
    createMainMenuHTML() {
        return `
            <div id="map-main-menu" class="map-modal-view map-modal-view-active" role="menu" aria-label="Accessibility categories">
                <div class="map-view-content">
                    <div class="map-category-grid">
                        <!-- Text Category -->
                        <button id="map-category-text" class="map-category-button" type="button" role="menuitem" data-category="text">
                            <div class="map-category-icon map-category-icon-text">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                    <polyline points="14,2 14,8 20,8"/>
                                    <line x1="16" y1="13" x2="8" y2="13"/>
                                    <line x1="16" y1="17" x2="8" y2="17"/>
                                </svg>
                            </div>
                            <div class="map-category-content">
                                <div class="map-category-title">Text</div>
                                <div class="map-category-desc">Reading and text options</div>
                            </div>
                            <div class="map-category-arrow" aria-hidden="true">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                                </svg>
                            </div>
                        </button>
                        
                        <!-- Colors Category -->
                        <button id="map-category-colors" class="map-category-button" type="button" role="menuitem" data-category="colors">
                            <div class="map-category-icon map-category-icon-colors">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="13.5" cy="6.5" r=".5"/>
                                    <circle cx="17.5" cy="10.5" r=".5"/>
                                    <circle cx="8.5" cy="7.5" r=".5"/>
                                    <circle cx="6.5" cy="12.5" r=".5"/>
                                    <path d="M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z"/>
                                </svg>
                            </div>
                            <div class="map-category-content">
                                <div class="map-category-title">Contrast & Colors</div>
                                <div class="map-category-desc">Contrast and color themes</div>
                            </div>
                            <div class="map-category-arrow" aria-hidden="true">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                                </svg>
                            </div>
                        </button>
                        
                        <!-- Navigation Category -->
                        <button id="map-category-navigation" class="map-category-button" type="button" role="menuitem" data-category="navigation">
                            <div class="map-category-icon map-category-icon-navigation">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <rect x="2" y="4" width="20" height="16" rx="2"/>
                                    <path d="M6 8h.01"/>
                                    <path d="M10 8h.01"/>
                                    <path d="M14 8h.01"/>
                                    <path d="M18 8h.01"/>
                                </svg>
                            </div>
                            <div class="map-category-content">
                                <div class="map-category-title">Navigation</div>
                                <div class="map-category-desc">Keyboard shortcuts and navigation aids</div>
                            </div>
                            <div class="map-category-arrow" aria-hidden="true">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                                </svg>
                            </div>
                        </button>
                        
                        <!-- Preferences Category -->
                        <button id="map-category-preferences" class="map-category-button" type="button" role="menuitem" data-category="preferences">
                            <div class="map-category-icon map-category-icon-preferences">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="12" cy="12" r="3"/>
                                    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                                </svg>
                            </div>
                            <div class="map-category-content">
                                <div class="map-category-title">Preferences</div>
                                <div class="map-category-desc">General settings and preferences</div>
                            </div>
                            <div class="map-category-arrow" aria-hidden="true">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                                </svg>
                            </div>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * Create placeholder views for other categories (Phase 1 - just structure)
     */
    createTextViewHTML() {
        return `<div id="map-view-text" class="map-modal-view" data-title="Text Options"><div class="map-view-content"><p style="text-align: center; padding: 2rem; color: #6b7280;">Text features will be implemented in Phase 2</p></div></div>`;
    }
    
    createColorsViewHTML() {
        return `<div id="map-view-colors" class="map-modal-view" data-title="Contrast & Colors"><div class="map-view-content"><p style="text-align: center; padding: 2rem; color: #6b7280;">Color features will be implemented in Phase 3</p></div></div>`;
    }
    
    createNavigationViewHTML() {
        return `<div id="map-view-navigation" class="map-modal-view" data-title="Navigation Options"><div class="map-view-content"><p style="text-align: center; padding: 2rem; color: #6b7280;">Navigation features will be implemented in Phase 4</p></div></div>`;
    }
    
    createPreferencesViewHTML() {
        return `<div id="map-view-preferences" class="map-modal-view" data-title="Preferences"><div class="map-view-content"><p style="text-align: center; padding: 2rem; color: #6b7280;">Preferences will be implemented in Phase 5</p></div></div>`;
    }
    
    /**
     * Bind event handlers
     */
    bindEvents() {
        // Main toggle button
        document.addEventListener('click', (e) => {
            if (e.target.closest('#map-main-toggle')) {
                e.preventDefault();
                this.toggleWidget();
            }
        });
        
        // Close panel button
        document.addEventListener('click', (e) => {
            if (e.target.closest('#map-close-panel')) {
                e.preventDefault();
                this.closeWidget();
            }
        });
        
        // Category buttons
        document.addEventListener('click', (e) => {
            const categoryBtn = e.target.closest('.map-category-button');
            if (categoryBtn) {
                e.preventDefault();
                const category = categoryBtn.dataset.category;
                if (category) {
                    this.showSection(category);
                }
            }
        });
        
        // Back buttons
        document.addEventListener('click', (e) => {
            if (e.target.closest('.map-header-back-button')) {
                e.preventDefault();
                this.showMainMenu();
            }
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.altKey && e.key === 'a') {
                e.preventDefault();
                this.toggleWidget();
            }
            if (e.key === 'Escape') {
                this.closeWidget();
            }
        });
    }
    
    /**
     * Toggle widget panel
     */
    toggleWidget() {
        const panel = document.getElementById('map-widget-panel');
        const toggle = document.getElementById('map-main-toggle');
        
        if (panel.style.display === 'none') {
            this.openWidget();
        } else {
            this.closeWidget();
        }
    }
    
    /**
     * Open widget panel
     */
    openWidget() {
        const toggle = document.getElementById('map-main-toggle');
        const panel = document.getElementById('map-widget-panel');
        const widget = document.getElementById('map-accessibility-widget');
        
        toggle.setAttribute('aria-expanded', 'true');
        panel.style.display = 'block';
        panel.setAttribute('aria-hidden', 'false');
        widget.classList.add('map-panel-active');
        toggle.classList.add('map-panel-open');
        
        // Ensure main menu is visible
        this.showMainMenu();
    }
    
    /**
     * Close widget panel
     */
    closeWidget() {
        const toggle = document.getElementById('map-main-toggle');
        const panel = document.getElementById('map-widget-panel');
        const widget = document.getElementById('map-accessibility-widget');
        
        toggle.setAttribute('aria-expanded', 'false');
        panel.style.display = 'none';
        panel.setAttribute('aria-hidden', 'true');
        widget.classList.remove('map-panel-active');
        toggle.classList.remove('map-panel-open');
        
        // Reset to main menu
        this.showMainMenu();
    }
    
    /**
     * Show main menu
     */
    showMainMenu() {
        this.currentView = 'main-menu';
        this.currentCategory = null;
        
        // Update header
        document.getElementById('map-panel-title').style.display = 'block';
        document.getElementById('map-header-navigation').style.display = 'none';
        
        // Switch view
        this.switchView('map-main-menu');
    }
    
    /**
     * Show category section
     */
    showSection(section) {
        this.previousView = this.currentView;
        this.currentView = section;
        this.currentCategory = section;
        
        // Update header
        document.getElementById('map-panel-title').style.display = 'none';
        document.getElementById('map-header-navigation').style.display = 'flex';
        
        // Set category title
        const titles = {
            text: 'Text Options',
            colors: 'Contrast & Colors',
            navigation: 'Navigation Options',
            preferences: 'Preferences'
        };
        document.getElementById('map-header-category-title').textContent = titles[section] || section;
        
        // Switch view
        this.switchView(`map-view-${section}`);
    }
    
    /**
     * Switch between modal views
     */
    switchView(targetViewId) {
        if (this.isTransitioning) return;
        
        this.isTransitioning = true;
        const currentView = document.querySelector('.map-modal-view-active');
        const targetView = document.getElementById(targetViewId);
        
        if (!targetView) {
            this.isTransitioning = false;
            return;
        }
        
        // Hide current view
        if (currentView) {
            currentView.classList.remove('map-modal-view-active');
            currentView.style.display = 'none';
        }
        
        // Show target view
        targetView.style.display = 'block';
        setTimeout(() => {
            targetView.classList.add('map-modal-view-active');
            this.isTransitioning = false;
        }, 50);
    }
    
    /**
     * Apply position class
     */
    applyPosition() {
        const widget = document.getElementById('map-accessibility-widget');
        if (widget) {
            widget.className = `map-accessibility-widget map-position-${this.features.position}`;
        }
    }
    
    /**
     * Load preferences from localStorage
     */
    loadPreferences() {
        try {
            const saved = localStorage.getItem('map-accessibility-preferences');
            if (saved) {
                const preferences = JSON.parse(saved);
                this.features = { ...this.features, ...preferences };
            }
        } catch (e) {
            console.warn('Could not load accessibility preferences:', e);
        }
    }
    
    /**
     * Save preferences to localStorage
     */
    savePreferences() {
        try {
            localStorage.setItem('map-accessibility-preferences', JSON.stringify(this.features));
        } catch (e) {
            console.warn('Could not save accessibility preferences:', e);
        }
    }
}

// Initialize widget when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // Only initialize if we're on the landing page and demo container exists
    if (document.getElementById('demo-container')) {
        window.accessibilityWidget = new StandaloneAccessibilityWidget();
    }
});
