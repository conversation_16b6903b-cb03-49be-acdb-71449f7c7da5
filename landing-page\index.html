<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WordPress Accessibility Plugin - Make Your Website Accessible to Everyone</title>
    <meta name="description" content="A premium WordPress plugin that enhances user experience for people with visual and cognitive challenges.">
    <link rel="stylesheet" href="style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="demo-widget.css">
</head>
<body>
    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title">Make Your Website Accessible to Everyone</h1>
                <p class="hero-subtitle">A premium WordPress plugin that enhances user experience for people with visual and cognitive challenges.</p>
                <div class="hero-cta">
                    <button class="btn btn-primary">Get Started Today</button>
                    <button class="btn btn-secondary">View Demo</button>
                </div>
            </div>
            <div class="hero-visual">
                <div class="accessibility-preview">
                    <div class="preview-screen">
                        <div class="preview-header">
                            <div class="preview-dots">
                                <span></span>
                                <span></span>
                                <span></span>
                            </div>
                        </div>
                        <div class="preview-content">
                            <div class="preview-text-line long"></div>
                            <div class="preview-text-line medium"></div>
                            <div class="preview-text-line short"></div>
                            <div class="preview-widget">
                                <div class="widget-icon"></div>
                                <div class="widget-controls">
                                    <div class="control-item"></div>
                                    <div class="control-item"></div>
                                    <div class="control-item"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Powerful Accessibility Features</h2>
                <p class="section-subtitle">Transform your website into an inclusive digital experience</p>
            </div>
            <div class="features-grid">
                <!-- Feature 1: Text-to-Speech -->
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"/>
                            <path d="M19 10v2a7 7 0 0 1-14 0v-2"/>
                            <line x1="12" y1="19" x2="12" y2="23"/>
                            <line x1="8" y1="23" x2="16" y2="23"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">Text-to-Speech</h3>
                    <p class="feature-description">Convert any text on your website into natural-sounding speech for visually impaired users.</p>
                </div>

                <!-- Feature 2: Dyslexia-Friendly Fonts -->
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                            <polyline points="14,2 14,8 20,8"/>
                            <line x1="16" y1="13" x2="8" y2="13"/>
                            <line x1="16" y1="17" x2="8" y2="17"/>
                            <polyline points="10,9 9,9 8,9"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">Dyslexia-Friendly Fonts</h3>
                    <p class="feature-description">Specially designed fonts that improve readability for users with dyslexia and reading difficulties.</p>
                </div>

                <!-- Feature 3: Color Contrast & Themes -->
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="3"/>
                            <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1m15.5-6.5l-4.24 4.24M7.76 7.76 3.52 3.52m12.96 12.96-4.24-4.24M7.76 16.24l-4.24 4.24"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">Color Contrast & Themes</h3>
                    <p class="feature-description">Multiple high-contrast themes and color adjustments for better visibility and reduced eye strain.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Live Demo Section -->
    <section class="demo">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">See It In Action</h2>
                <p class="section-subtitle">Experience the accessibility features live on this page</p>
            </div>
            <div class="demo-container">
                <div class="demo-placeholder">
                    <div class="demo-icon">
                        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"/>
                            <polygon points="10,8 16,12 10,16 10,8"/>
                        </svg>
                    </div>
                    <h3>🎯 Live Demo Active!</h3>
                    <p>The accessibility widget is now live on this page! Look for the floating button in the bottom-right corner to test all features.</p>
                    <div style="margin-top: 20px; padding: 15px; background: #f0f9ff; border-radius: 8px; border-left: 4px solid #6366f1;">
                        <strong>👆 Try it now:</strong> Click the accessibility button to explore text-to-speech, themes, font adjustments, and more!
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <p>&copy; 2025 WordPress Accessibility Plugin – All rights reserved.</p>
            </div>
        </div>
    </footer>



    <!-- Accessibility Widget Demo -->
    <!-- Standalone Accessibility Widget - Extracted from Original Plugin -->
    <div id="map-accessibility-widget" class="map-accessibility-widget map-position-bottom-right map-style-modern map-size-medium" role="region" aria-label="Accessibility Tools">

        <!-- Main Toggle Button -->
        <button id="map-main-toggle" class="map-main-toggle" type="button" aria-expanded="false" aria-controls="map-widget-panel" aria-label="Open accessibility tools">
            <span class="map-toggle-icon" aria-hidden="true">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2M12 8C13.1 8 14 8.9 14 10C14 11.1 13.1 12 12 12C10.9 12 10 11.1 10 10C10 8.9 10.9 8 12 8M12 14C13.1 14 14 14.9 14 16C14 17.1 13.1 18 12 18C10.9 18 10 17.1 10 16C10 14.9 10.9 14 12 14M6 8C7.1 8 8 8.9 8 10C8 11.1 7.1 12 6 12C4.9 12 4 11.1 4 10C4 8.9 4.9 8 6 8M18 8C19.1 8 20 8.9 20 10C20 11.1 19.1 12 18 12C16.9 12 16 11.1 16 10C16 8.9 16.9 8 18 8M6 14C7.1 14 8 14.9 8 16C8 17.1 7.1 18 6 18C4.9 18 4 17.1 4 16C4 14.9 4.9 14 6 14M18 14C19.1 14 20 14.9 20 16C20 17.1 19.1 18 18 18C16.9 18 16 17.1 16 16C16 14.9 16.9 14 18 14"/>
                </svg>
            </span>
        </button>

        <!-- Widget Panel -->
        <div id="map-widget-panel" class="map-widget-panel" style="display: none;" aria-hidden="true">
            <div class="map-panel-header">
                <!-- Main title - shown when on main menu -->
                <h3 id="map-panel-title" class="map-panel-title">Accessibility Tools</h3>

                <!-- Back button with category title - shown when in category views -->
                <div id="map-header-navigation" class="map-header-navigation" style="display: none;">
                    <button id="map-header-back-button" class="map-header-back-button" type="button" aria-label="Back to main menu">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M15.41 16.59L10.83 12l4.58-4.59L14 6l-6 6 6 6 1.41-1.41z"/>
                        </svg>
                    </button>
                    <h4 id="map-header-category-title" class="map-header-category-title"></h4>
                </div>

                <div class="map-header-buttons">
                    <button id="map-reset-category" class="map-reset-button" type="button" aria-label="Reset category options to default" style="display: none;">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
                        </svg>
                        <span>Reset</span>
                    </button>
                    <button id="map-close-panel" class="map-close-button" type="button" aria-label="Close accessibility tools">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                        </svg>
                    </button>
                </div>
            </div>

            <div class="map-panel-content">
                <!-- Premium Animation Container for Modal Views -->
                <div class="map-modal-views-container">
                    <!-- Main Menu (Level 1) - Category Selection -->
                    <div id="map-main-menu" class="map-modal-view map-modal-view-active" role="menu" aria-label="Accessibility categories">
                        <div class="map-category-grid">
                            <!-- Text Category -->
                            <button id="map-category-text" class="map-category-button" type="button" role="menuitem" data-category="text" aria-describedby="map-category-text-desc">
                                <div class="map-category-icon map-category-icon-text">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                        <polyline points="14,2 14,8 20,8"/>
                                        <line x1="16" y1="13" x2="8" y2="13"/>
                                        <line x1="16" y1="17" x2="8" y2="17"/>
                                        <polyline points="10,9 9,9 8,9"/>
                                    </svg>
                                </div>
                                <div class="map-category-content">
                                    <div class="map-category-title">Text</div>
                                    <div id="map-category-text-desc" class="map-category-desc">Reading and text options</div>
                                </div>
                                <div class="map-category-arrow" aria-hidden="true">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                                    </svg>
                                </div>
                            </button>

                            <!-- Colors Category -->
                            <button id="map-category-colors" class="map-category-button" type="button" role="menuitem" data-category="colors" aria-describedby="map-category-colors-desc">
                                <div class="map-category-icon map-category-icon-colors">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <circle cx="13.5" cy="6.5" r=".5"/>
                                        <circle cx="17.5" cy="10.5" r=".5"/>
                                        <circle cx="8.5" cy="7.5" r=".5"/>
                                        <circle cx="6.5" cy="12.5" r=".5"/>
                                        <path d="M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z"/>
                                    </svg>
                                </div>
                                <div class="map-category-content">
                                    <div class="map-category-title">Contrast & Colors</div>
                                    <div id="map-category-colors-desc" class="map-category-desc">Contrast and color themes</div>
                                </div>
                                <div class="map-category-arrow" aria-hidden="true">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                                    </svg>
                                </div>
                            </button>

                            <!-- Navigation Category -->
                            <button id="map-category-navigation" class="map-category-button" type="button" role="menuitem" data-category="navigation" aria-describedby="map-category-navigation-desc">
                                <div class="map-category-icon map-category-icon-navigation">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <rect x="2" y="4" width="20" height="16" rx="2"/>
                                        <path d="M6 8h.01"/>
                                        <path d="M10 8h.01"/>
                                        <path d="M14 8h.01"/>
                                        <path d="M18 8h.01"/>
                                        <path d="M8 12h.01"/>
                                        <path d="M12 12h.01"/>
                                        <path d="M16 12h.01"/>
                                        <path d="M7 16h10"/>
                                    </svg>
                                </div>
                                <div class="map-category-content">
                                    <div class="map-category-title">Navigation</div>
                                    <div id="map-category-navigation-desc" class="map-category-desc">Keyboard shortcuts and navigation aids</div>
                                </div>
                                <div class="map-category-arrow" aria-hidden="true">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                                    </svg>
                                </div>
                            </button>

                            <!-- Preferences Category -->
                            <button id="map-category-preferences" class="map-category-button" type="button" role="menuitem" data-category="preferences" aria-describedby="map-category-preferences-desc">
                                <div class="map-category-icon map-category-icon-preferences">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <circle cx="12" cy="12" r="3"/>
                                        <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                                    </svg>
                                </div>
                                <div class="map-category-content">
                                    <div class="map-category-title">Preferences</div>
                                    <div id="map-category-preferences-desc" class="map-category-desc">General settings and preferences</div>
                                </div>
                                <div class="map-category-arrow" aria-hidden="true">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                                    </svg>
                                </div>
                            </button>
                        </div>
                    </div>

                    <!-- Note: Other UI views (Text, Colors, Navigation, Preferences) will be loaded dynamically -->
                    <!-- This keeps the initial HTML lightweight while maintaining full functionality -->

                </div> <!-- Close map-modal-views-container -->
            </div>
        </div>
    </div>

    <script src="script.js"></script>
    <script src="demo-widget.js"></script>
</body>
</html>
