<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WordPress Accessibility Plugin - Make Your Website Accessible to Everyone</title>
    <meta name="description" content="A premium WordPress plugin that enhances user experience for people with visual and cognitive challenges.">
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="accessibility-widget.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title">Make Your Website Accessible to Everyone</h1>
                <p class="hero-subtitle">A premium WordPress plugin that enhances user experience for people with visual and cognitive challenges.</p>
                <div class="hero-cta">
                    <button class="btn btn-primary">Get Started Today</button>
                    <button class="btn btn-secondary">View Demo</button>
                </div>
            </div>
            <div class="hero-visual">
                <div class="accessibility-preview">
                    <div class="preview-screen">
                        <div class="preview-header">
                            <div class="preview-dots">
                                <span></span>
                                <span></span>
                                <span></span>
                            </div>
                        </div>
                        <div class="preview-content">
                            <div class="preview-text-line long"></div>
                            <div class="preview-text-line medium"></div>
                            <div class="preview-text-line short"></div>
                            <div class="preview-widget">
                                <div class="widget-icon"></div>
                                <div class="widget-controls">
                                    <div class="control-item"></div>
                                    <div class="control-item"></div>
                                    <div class="control-item"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Powerful Accessibility Features</h2>
                <p class="section-subtitle">Transform your website into an inclusive digital experience</p>
            </div>
            <div class="features-grid">
                <!-- Feature 1: Text-to-Speech -->
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"/>
                            <path d="M19 10v2a7 7 0 0 1-14 0v-2"/>
                            <line x1="12" y1="19" x2="12" y2="23"/>
                            <line x1="8" y1="23" x2="16" y2="23"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">Text-to-Speech</h3>
                    <p class="feature-description">Convert any text on your website into natural-sounding speech for visually impaired users.</p>
                </div>

                <!-- Feature 2: Dyslexia-Friendly Fonts -->
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                            <polyline points="14,2 14,8 20,8"/>
                            <line x1="16" y1="13" x2="8" y2="13"/>
                            <line x1="16" y1="17" x2="8" y2="17"/>
                            <polyline points="10,9 9,9 8,9"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">Dyslexia-Friendly Fonts</h3>
                    <p class="feature-description">Specially designed fonts that improve readability for users with dyslexia and reading difficulties.</p>
                </div>

                <!-- Feature 3: Color Contrast & Themes -->
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="3"/>
                            <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1m15.5-6.5l-4.24 4.24M7.76 7.76 3.52 3.52m12.96 12.96-4.24-4.24M7.76 16.24l-4.24 4.24"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">Color Contrast & Themes</h3>
                    <p class="feature-description">Multiple high-contrast themes and color adjustments for better visibility and reduced eye strain.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Live Demo Section -->
    <section class="demo">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">See It In Action</h2>
                <p class="section-subtitle">Experience the accessibility features live on this page</p>
            </div>
            <div class="demo-container">
                <!-- Live Demo Container - Accessibility Widget will be injected here -->
                <div id="demo-container" class="demo-live-container">
                    <div class="demo-instructions">
                        <h3>🎯 Live Demo - Try It Now!</h3>
                        <p>Click the accessibility button below to explore all features. The widget works exactly like it would on your WordPress site.</p>
                        <div class="demo-features">
                            <span class="demo-feature">✨ Text-to-Speech</span>
                            <span class="demo-feature">🎨 Visual Themes</span>
                            <span class="demo-feature">🔍 Reading Tools</span>
                            <span class="demo-feature">⚙️ Preferences</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <p>&copy; 2025 WordPress Accessibility Plugin – All rights reserved.</p>
            </div>
        </div>
    </footer>

   

    <script src="script.js"></script>
    <script src="accessibility-widget.js"></script>
</body>
</html>
