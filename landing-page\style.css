/**
 * WordPress Accessibility Plugin Landing Page
 * Modern, responsive design matching the plugin's color scheme
 */

/* ===== CSS VARIABLES - MATCHING PLUGIN COLORS ===== */
:root {
    /* Primary Brand Colors - From Plugin */
    --primary: #6366f1;
    --primary-light: #818cf8;
    --primary-dark: #4f46e5;
    --accent: #f59e0b;
    --accent-light: #fbbf24;

    /* Neutral Palette */
    --white: #ffffff;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    /* Semantic Colors */
    --success: #059669;
    --warning: #d97706;
    --error: #dc2626;
    --info: #0284c7;

    /* Design Tokens */
    --radius-sm: 6px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 24px;
    --radius-full: 9999px;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    --space-24: 6rem;

    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    --font-size-6xl: 3.75rem;

    /* Transitions */
    --transition-base: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--gray-700);
    background-color: var(--white);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* ===== LAYOUT COMPONENTS ===== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-6);
}

@media (max-width: 768px) {
    .container {
        padding: 0 var(--space-4);
    }
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    color: var(--gray-900);
    margin-bottom: var(--space-4);
}

h1 {
    font-size: var(--font-size-5xl);
    font-weight: 700;
}

h2 {
    font-size: var(--font-size-4xl);
}

h3 {
    font-size: var(--font-size-2xl);
}

@media (max-width: 768px) {
    h1 {
        font-size: var(--font-size-4xl);
    }
    
    h2 {
        font-size: var(--font-size-3xl);
    }
}

/* ===== BUTTONS ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-6);
    font-size: var(--font-size-base);
    font-weight: 500;
    text-decoration: none;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition-base);
    white-space: nowrap;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
    color: var(--white);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary) 100%);
}

.btn-secondary {
    background: var(--white);
    color: var(--gray-700);
    border: 2px solid var(--gray-300);
    box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
    background: var(--gray-50);
    border-color: var(--primary);
    color: var(--primary);
    transform: translateY(-1px);
}

/* ===== HERO SECTION ===== */
.hero {
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
    padding: var(--space-24) 0;
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(245, 158, 11, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.hero .container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-16);
    align-items: center;
    position: relative;
    z-index: 1;
}

.hero-content {
    max-width: 600px;
}

.hero-title {
    font-size: var(--font-size-6xl);
    font-weight: 700;
    background: linear-gradient(135deg, var(--gray-900) 0%, var(--primary) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--space-6);
    line-height: 1.1;
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    color: var(--gray-600);
    margin-bottom: var(--space-8);
    line-height: 1.6;
}

.hero-cta {
    display: flex;
    gap: var(--space-4);
    flex-wrap: wrap;
}

@media (max-width: 1024px) {
    .hero .container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--space-12);
    }
    
    .hero-title {
        font-size: var(--font-size-5xl);
    }
}

@media (max-width: 768px) {
    .hero {
        padding: var(--space-16) 0;
        min-height: auto;
    }
    
    .hero-title {
        font-size: var(--font-size-4xl);
    }
    
    .hero-subtitle {
        font-size: var(--font-size-lg);
    }
    
    .hero-cta {
        justify-content: center;
    }
}

/* ===== HERO VISUAL ===== */
.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.accessibility-preview {
    position: relative;
    transform: perspective(1000px) rotateY(-5deg) rotateX(5deg);
    transition: var(--transition-base);
}

.accessibility-preview:hover {
    transform: perspective(1000px) rotateY(-2deg) rotateX(2deg) scale(1.02);
}

.preview-screen {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    width: 400px;
    height: 300px;
    border: 1px solid var(--gray-200);
}

.preview-header {
    background: var(--gray-100);
    padding: var(--space-4);
    border-bottom: 1px solid var(--gray-200);
}

.preview-dots {
    display: flex;
    gap: var(--space-2);
}

.preview-dots span {
    width: 12px;
    height: 12px;
    border-radius: var(--radius-full);
    background: var(--gray-300);
}

.preview-dots span:nth-child(1) {
    background: #ff5f57;
}

.preview-dots span:nth-child(2) {
    background: #ffbd2e;
}

.preview-dots span:nth-child(3) {
    background: #28ca42;
}

.preview-content {
    padding: var(--space-6);
    height: calc(100% - 60px);
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
}

.preview-text-line {
    height: 8px;
    background: var(--gray-200);
    border-radius: var(--radius-sm);
    animation: pulse 2s infinite;
}

.preview-text-line.long {
    width: 100%;
}

.preview-text-line.medium {
    width: 75%;
}

.preview-text-line.short {
    width: 50%;
}

.preview-widget {
    margin-top: auto;
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    display: flex;
    align-items: center;
    gap: var(--space-3);
    box-shadow: var(--shadow-md);
}

.widget-icon {
    width: 24px;
    height: 24px;
    background: var(--white);
    border-radius: var(--radius-sm);
    opacity: 0.9;
}

.widget-controls {
    display: flex;
    gap: var(--space-2);
    flex: 1;
}

.control-item {
    height: 6px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: var(--radius-sm);
    flex: 1;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

@media (max-width: 1024px) {
    .preview-screen {
        width: 300px;
        height: 225px;
    }
}

@media (max-width: 768px) {
    .accessibility-preview {
        transform: none;
    }

    .accessibility-preview:hover {
        transform: scale(1.02);
    }

    .preview-screen {
        width: 280px;
        height: 210px;
    }
}

/* ===== SECTION STYLES ===== */
.section-header {
    text-align: center;
    margin-bottom: var(--space-16);
}

.section-title {
    margin-bottom: var(--space-4);
}

.section-subtitle {
    font-size: var(--font-size-xl);
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
}

/* ===== FEATURES SECTION ===== */
.features {
    padding: var(--space-24) 0;
    background: var(--white);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-8);
}

.feature-card {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-xl);
    padding: var(--space-8);
    text-align: center;
    transition: var(--transition-base);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary), var(--accent));
    opacity: 0;
    transition: var(--transition-base);
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary);
}

.feature-card:hover::before {
    opacity: 1;
}

.feature-icon {
    width: 64px;
    height: 64px;
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-6);
    color: var(--white);
    transition: var(--transition-base);
}

.feature-card:hover .feature-icon {
    transform: scale(1.1) rotate(5deg);
}

.feature-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--space-4);
}

.feature-description {
    color: var(--gray-600);
    line-height: 1.6;
}

@media (max-width: 768px) {
    .features {
        padding: var(--space-16) 0;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: var(--space-6);
    }

    .feature-card {
        padding: var(--space-6);
    }
}

/* ===== DEMO SECTION ===== */
.demo {
    padding: var(--space-24) 0;
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
    position: relative;
}

.demo::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 70% 30%, rgba(99, 102, 241, 0.05) 0%, transparent 50%);
    pointer-events: none;
}

.demo-container {
    position: relative;
    z-index: 1;
}

.demo-placeholder {
    background: var(--white);
    border: 2px dashed var(--gray-300);
    border-radius: var(--radius-xl);
    padding: var(--space-16);
    text-align: center;
    min-height: 400px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: var(--transition-base);
}

.demo-placeholder:hover {
    border-color: var(--primary);
    background: var(--gray-50);
}

.demo-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--space-6);
    color: var(--white);
}

.demo-placeholder h3 {
    font-size: var(--font-size-2xl);
    color: var(--gray-900);
    margin-bottom: var(--space-4);
}

.demo-placeholder p {
    color: var(--gray-600);
    font-size: var(--font-size-lg);
    max-width: 500px;
}

@media (max-width: 768px) {
    .demo {
        padding: var(--space-16) 0;
    }

    .demo-placeholder {
        padding: var(--space-12);
        min-height: 300px;
    }

    .demo-icon {
        width: 60px;
        height: 60px;
    }
}

/* ===== FOOTER ===== */
.footer {
    background: var(--gray-900);
    color: var(--gray-300);
    padding: var(--space-12) 0;
    text-align: center;
}

.footer-content p {
    margin: 0;
    font-size: var(--font-size-sm);
}

/* ===== FLOATING DEMO BUTTON ===== */
.floating-demo {
    position: fixed;
    bottom: var(--space-8);
    right: var(--space-8);
    z-index: 1000;
}

.demo-btn {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
    color: var(--white);
    border: none;
    border-radius: var(--radius-full);
    padding: var(--space-4) var(--space-6);
    font-size: var(--font-size-base);
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--space-2);
    box-shadow: var(--shadow-lg);
    transition: var(--transition-base);
    animation: float 3s ease-in-out infinite;
}

.demo-btn:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: var(--shadow-xl);
    background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary) 100%);
}

.demo-btn:active {
    transform: translateY(0) scale(0.98);
}

@keyframes float {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-5px);
    }
}

@media (max-width: 768px) {
    .floating-demo {
        bottom: var(--space-6);
        right: var(--space-6);
    }

    .demo-btn {
        padding: var(--space-3) var(--space-5);
        font-size: var(--font-size-sm);
    }
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */
/* Focus styles for keyboard navigation */
*:focus-visible {
    outline: 3px solid var(--primary);
    outline-offset: 2px;
    border-radius: var(--radius-sm);
}

/* Reduced motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .btn-primary {
        border: 2px solid var(--white);
    }

    .btn-secondary {
        border-width: 3px;
    }

    .feature-card {
        border-width: 2px;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --white: #111827;
        --gray-50: #1f2937;
        --gray-100: #374151;
        --gray-200: #4b5563;
        --gray-300: #6b7280;
        --gray-700: #d1d5db;
        --gray-900: #f9fafb;
    }
}
