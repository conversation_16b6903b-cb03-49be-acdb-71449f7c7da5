<!-- Standalone Accessibility Widget - Extracted from Original Plugin -->
<div id="map-accessibility-widget" class="map-accessibility-widget map-position-bottom-right map-style-modern map-size-medium" role="region" aria-label="Accessibility Tools">
    
    <!-- Main Toggle Button -->
    <button id="map-main-toggle" class="map-main-toggle" type="button" aria-expanded="false" aria-controls="map-widget-panel" aria-label="Open accessibility tools">
        <span class="map-toggle-icon" aria-hidden="true">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2M12 8C13.1 8 14 8.9 14 10C14 11.1 13.1 12 12 12C10.9 12 10 11.1 10 10C10 8.9 10.9 8 12 8M12 14C13.1 14 14 14.9 14 16C14 17.1 13.1 18 12 18C10.9 18 10 17.1 10 16C10 14.9 10.9 14 12 14M6 8C7.1 8 8 8.9 8 10C8 11.1 7.1 12 6 12C4.9 12 4 11.1 4 10C4 8.9 4.9 8 6 8M18 8C19.1 8 20 8.9 20 10C20 11.1 19.1 12 18 12C16.9 12 16 11.1 16 10C16 8.9 16.9 8 18 8M6 14C7.1 14 8 14.9 8 16C8 17.1 7.1 18 6 18C4.9 18 4 17.1 4 16C4 14.9 4.9 14 6 14M18 14C19.1 14 20 14.9 20 16C20 17.1 19.1 18 18 18C16.9 18 16 17.1 16 16C16 14.9 16.9 14 18 14"/>
            </svg>
        </span>
    </button>
    
    <!-- Widget Panel -->
    <div id="map-widget-panel" class="map-widget-panel" style="display: none;" aria-hidden="true">
        <div class="map-panel-header">
            <!-- Main title - shown when on main menu -->
            <h3 id="map-panel-title" class="map-panel-title">Accessibility Tools</h3>

            <!-- Back button with category title - shown when in category views -->
            <div id="map-header-navigation" class="map-header-navigation" style="display: none;">
                <button id="map-header-back-button" class="map-header-back-button" type="button" aria-label="Back to main menu">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M15.41 16.59L10.83 12l4.58-4.59L14 6l-6 6 6 6 1.41-1.41z"/>
                    </svg>
                </button>
                <h4 id="map-header-category-title" class="map-header-category-title"></h4>
            </div>

            <div class="map-header-buttons">
                <button id="map-reset-category" class="map-reset-button" type="button" aria-label="Reset category options to default" style="display: none;">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
                    </svg>
                    <span>Reset</span>
                </button>
                <button id="map-close-panel" class="map-close-button" type="button" aria-label="Close accessibility tools">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                    </svg>
                </button>
            </div>
        </div>

        <div class="map-panel-content">
            <!-- Premium Animation Container for Modal Views -->
            <div class="map-modal-views-container">
                <!-- Main Menu (Level 1) - Category Selection -->
                <div id="map-main-menu" class="map-modal-view map-modal-view-active" role="menu" aria-label="Accessibility categories">
                    <div class="map-category-grid">
                        <!-- Text Category -->
                        <button id="map-category-text" class="map-category-button" type="button" role="menuitem" data-category="text" aria-describedby="map-category-text-desc">
                            <div class="map-category-icon map-category-icon-text">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                    <polyline points="14,2 14,8 20,8"/>
                                    <line x1="16" y1="13" x2="8" y2="13"/>
                                    <line x1="16" y1="17" x2="8" y2="17"/>
                                    <polyline points="10,9 9,9 8,9"/>
                                </svg>
                            </div>
                            <div class="map-category-content">
                                <div class="map-category-title">Text</div>
                                <div id="map-category-text-desc" class="map-category-desc">Reading and text options</div>
                            </div>
                            <div class="map-category-arrow" aria-hidden="true">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                                </svg>
                            </div>
                        </button>

                        <!-- Colors Category -->
                        <button id="map-category-colors" class="map-category-button" type="button" role="menuitem" data-category="colors" aria-describedby="map-category-colors-desc">
                            <div class="map-category-icon map-category-icon-colors">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <circle cx="13.5" cy="6.5" r=".5"/>
                                    <circle cx="17.5" cy="10.5" r=".5"/>
                                    <circle cx="8.5" cy="7.5" r=".5"/>
                                    <circle cx="6.5" cy="12.5" r=".5"/>
                                    <path d="M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z"/>
                                </svg>
                            </div>
                            <div class="map-category-content">
                                <div class="map-category-title">Contrast & Colors</div>
                                <div id="map-category-colors-desc" class="map-category-desc">Contrast and color themes</div>
                            </div>
                            <div class="map-category-arrow" aria-hidden="true">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                                </svg>
                            </div>
                        </button>

                        <!-- Navigation Category -->
                        <button id="map-category-navigation" class="map-category-button" type="button" role="menuitem" data-category="navigation" aria-describedby="map-category-navigation-desc">
                            <div class="map-category-icon map-category-icon-navigation">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <rect x="2" y="4" width="20" height="16" rx="2"/>
                                    <path d="M6 8h.01"/>
                                    <path d="M10 8h.01"/>
                                    <path d="M14 8h.01"/>
                                    <path d="M18 8h.01"/>
                                    <path d="M8 12h.01"/>
                                    <path d="M12 12h.01"/>
                                    <path d="M16 12h.01"/>
                                    <path d="M7 16h10"/>
                                </svg>
                            </div>
                            <div class="map-category-content">
                                <div class="map-category-title">Navigation</div>
                                <div id="map-category-navigation-desc" class="map-category-desc">Keyboard shortcuts and navigation aids</div>
                            </div>
                            <div class="map-category-arrow" aria-hidden="true">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                                </svg>
                            </div>
                        </button>

                        <!-- Preferences Category -->
                        <button id="map-category-preferences" class="map-category-button" type="button" role="menuitem" data-category="preferences" aria-describedby="map-category-preferences-desc">
                            <div class="map-category-icon map-category-icon-preferences">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <circle cx="12" cy="12" r="3"/>
                                    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                                </svg>
                            </div>
                            <div class="map-category-content">
                                <div class="map-category-title">Preferences</div>
                                <div id="map-category-preferences-desc" class="map-category-desc">General settings and preferences</div>
                            </div>
                            <div class="map-category-arrow" aria-hidden="true">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                                </svg>
                            </div>
                        </button>
                    </div>
                </div>

                <!-- Text Category View (Level 2) - UI #2 -->
                <div id="map-view-text" class="map-modal-view" role="region" aria-label="Text accessibility options" data-title="Text Options">
                    <div class="map-view-content">
                        <!-- Text-to-Speech Section -->
                        <div class="map-feature-section">
                            <button id="map-tts-toggle" class="map-feature-toggle" data-active="false" type="button">
                                <div class="map-feature-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"/>
                                    </svg>
                                </div>
                                <div class="map-feature-content">
                                    <div class="map-feature-title">Text to Speech</div>
                                    <div class="map-feature-desc">Select text to hear it read aloud</div>
                                </div>
                                <div class="map-toggle-switch">
                                    <span class="map-toggle-slider"></span>
                                </div>
                            </button>
                        </div>

                        <!-- Dyslexic Font Toggle Section -->
                        <div class="map-feature-section">
                            <button id="map-dyslexic-font-toggle" class="map-feature-toggle" data-active="false" type="button">
                                <div class="map-feature-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M4 3h8c5.5 0 10 4.5 10 10s-4.5 10-10 10H4V3zm3 3v14h5c3.9 0 7-3.1 7-7s-3.1-7-7-7H7z"/>
                                    </svg>
                                </div>
                                <div class="map-feature-content">
                                    <div class="map-feature-title">Enable Dyslexic Font</div>
                                    <div class="map-feature-desc">Apply dyslexia-friendly font to improve readability</div>
                                </div>
                                <div class="map-toggle-switch">
                                    <span class="map-toggle-slider"></span>
                                </div>
                            </button>
                        </div>

                        <!-- Reading Guide Section -->
                        <div class="map-feature-section">
                            <button id="map-reading-guide-toggle" class="map-feature-toggle" data-active="false" type="button">
                                <div class="map-feature-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <g transform="rotate(30 12 12)">
                                            <rect x="2" y="8" width="20" height="8" fill="none" stroke="currentColor" stroke-width="2"/>
                                            <line x1="4" y1="16" x2="4" y2="14" stroke="currentColor" stroke-width="1.5"/>
                                            <line x1="7" y1="16" x2="7" y2="14" stroke="currentColor" stroke-width="1.5"/>
                                            <line x1="10" y1="16" x2="10" y2="13.5" stroke="currentColor" stroke-width="2"/>
                                            <line x1="13" y1="16" x2="13" y2="14" stroke="currentColor" stroke-width="1.5"/>
                                            <line x1="16" y1="16" x2="16" y2="14" stroke="currentColor" stroke-width="1.5"/>
                                            <line x1="19" y1="16" x2="19" y2="14" stroke="currentColor" stroke-width="1.5"/>
                                            <circle cx="18" cy="10.5" r="1.5" fill="none" stroke="currentColor" stroke-width="1.5"/>
                                            <circle cx="18" cy="10.5" r="0.5" fill="currentColor"/>
                                        </g>
                                    </svg>
                                </div>
                                <div class="map-feature-content">
                                    <div class="map-feature-title">Reading Guide</div>
                                    <div class="map-feature-desc">Show a horizontal line that follows your mouse to help focus on text</div>
                                </div>
                                <div class="map-toggle-switch">
                                    <span class="map-toggle-slider"></span>
                                </div>
                            </button>
                        </div>

                        <!-- Font Size Control Section -->
                        <div class="map-feature-section">
                            <button id="map-font-size-toggle" class="map-feature-toggle" data-active="false" type="button">
                                <div class="map-feature-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M8 2h14v4h-5v16h-4V6H8V2z"/>
                                        <path d="M2 12h8v2.5H7v7.5H5v-7.5H2V12z"/>
                                    </svg>
                                </div>
                                <div class="map-feature-content">
                                    <div class="map-feature-title">Font Size</div>
                                    <div class="map-feature-desc">Adjust text size for better readability</div>
                                </div>
                                <div class="map-feature-status">
                                    <span id="map-font-size-value">Default</span>
                                </div>
                                <div class="map-category-arrow">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M9 18l6-6-6-6"/>
                                    </svg>
                                </div>
                            </button>

                            <div id="map-font-size-controls" class="map-feature-controls" style="display: none;">
                                <div class="map-font-size-controls">
                                    <div class="map-size-control-group">
                                        <button id="map-font-size-decrease" class="map-size-control-btn map-size-decrease" type="button" aria-label="Decrease font size">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round">
                                                <path d="M5 12h14"/>
                                            </svg>
                                        </button>

                                        <div class="map-size-indicator">
                                            <div class="map-size-preview">Aa</div>
                                            <div class="map-size-percentage" id="map-font-percentage">100%</div>
                                        </div>

                                        <button id="map-font-size-increase" class="map-size-control-btn map-size-increase" type="button" aria-label="Increase font size">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round">
                                                <path d="M12 5v14M5 12h14"/>
                                            </svg>
                                        </button>
                                    </div>

                                    <button id="map-font-size-reset" class="map-control-reset" type="button" aria-label="Reset font size to default">
                                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
                                            <path d="M21 3v5h-5"/>
                                            <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
                                            <path d="M3 21v-5h5"/>
                                        </svg>
                                        Reset to Default
                                    </button>
                                </div>
                            </div>

                            <div id="map-font-size-status" class="map-status-message" style="display: none;">
                                <div class="map-status-icon">ℹ️</div>
                                <div class="map-status-text">Font size has been adjusted. The change applies to all text on the website.</div>
                            </div>
                        </div>

                        <!-- Line Spacing Control Section -->
                        <div class="map-feature-section">
                            <button id="map-line-spacing-toggle" class="map-feature-toggle" data-active="false" type="button">
                                <div class="map-feature-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M6 7h12v2H6V7zm0 4h12v2H6v-2zm0 4h12v2H6v-2z"/>
                                        <path d="M3 4h2v16H3V4z"/>
                                        <path d="M19 4h2v16h-2V4z"/>
                                        <circle cx="4" cy="6" r="1" fill="currentColor"/>
                                        <circle cx="4" cy="12" r="1" fill="currentColor"/>
                                        <circle cx="4" cy="18" r="1" fill="currentColor"/>
                                        <circle cx="20" cy="6" r="1" fill="currentColor"/>
                                        <circle cx="20" cy="12" r="1" fill="currentColor"/>
                                        <circle cx="20" cy="18" r="1" fill="currentColor"/>
                                    </svg>
                                </div>
                                <div class="map-feature-content">
                                    <div class="map-feature-title">Line Spacing</div>
                                    <div class="map-feature-desc">Adjust space between lines for better readability</div>
                                </div>
                                <div class="map-feature-status">
                                    <span id="map-line-spacing-value">Default</span>
                                </div>
                                <div class="map-category-arrow">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M9 18l6-6-6-6"/>
                                    </svg>
                                </div>
                            </button>

                            <div id="map-line-spacing-controls" class="map-feature-controls" style="display: none;">
                                <div class="map-line-spacing-controls">
                                    <div class="map-spacing-control-group">
                                        <div class="map-spacing-labels">
                                            <span class="map-spacing-label-min">Tight</span>
                                            <span class="map-spacing-label-center">Normal</span>
                                            <span class="map-spacing-label-max">Wide</span>
                                        </div>

                                        <div class="map-slider-container">
                                            <input type="range" id="map-line-spacing-slider" class="map-premium-slider" min="1.0" max="2.5" step="0.1" value="1.5" aria-label="Adjust line spacing">
                                            <div class="map-slider-track">
                                                <div class="map-slider-progress" id="map-slider-progress"></div>
                                            </div>
                                        </div>

                                        <div class="map-spacing-value">
                                            <span id="map-spacing-numeric">1.5x</span>
                                        </div>
                                    </div>

                                    <button id="map-line-spacing-reset" class="map-control-reset" type="button" aria-label="Reset line spacing to default">
                                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
                                            <path d="M21 3v5h-5"/>
                                            <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
                                            <path d="M3 21v-5h5"/>
                                        </svg>
                                        Reset to Default
                                    </button>
                                </div>
                            </div>

                            <div id="map-line-spacing-status" class="map-status-message" style="display: none;">
                                <div class="map-status-icon">ℹ️</div>
                                <div class="map-status-text">Line spacing has been adjusted. The change applies to all text on the website.</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Navigation Category View (Level 2) - UI #3 -->
                <div id="map-view-navigation" class="map-modal-view" role="region" aria-label="Navigation accessibility options" data-title="Navigation Options">
                    <div class="map-view-content">
                        <!-- ADHD Focus Mode Section -->
                        <div class="map-feature-section">
                            <button id="map-nav-adhd-focus-toggle" class="map-feature-toggle" data-active="false" type="button">
                                <div class="map-feature-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="1.5" fill="none" opacity="0.4"/>
                                        <circle cx="12" cy="12" r="7" stroke="currentColor" stroke-width="2" fill="none" opacity="0.7"/>
                                        <circle cx="12" cy="12" r="4" fill="currentColor"/>
                                        <circle cx="12" cy="12" r="1.5" fill="white"/>
                                        <path d="M12 2v4M12 18v4M2 12h4M18 12h4" stroke="currentColor" stroke-width="2" opacity="0.6"/>
                                        <path d="M4 4l2 2M4 20l2-2M20 4l-2 2M20 20l-2-2" stroke="currentColor" stroke-width="1.5" opacity="0.5"/>
                                    </svg>
                                </div>
                                <div class="map-feature-content">
                                    <div class="map-feature-title">ADHD Focus Mode</div>
                                    <div class="map-feature-desc">Reduce distractions and highlight content for better focus</div>
                                </div>
                                <div class="map-toggle-switch">
                                    <span class="map-toggle-slider"></span>
                                </div>
                            </button>

                            <div id="map-nav-adhd-focus-status" class="map-status-message" style="display: none;">
                                <div class="map-status-icon">🎯</div>
                                <div class="map-status-text">ADHD Focus Mode is active. Content is highlighted and distractions are minimized.</div>
                            </div>
                        </div>

                        <!-- Big Cursor Section -->
                        <div class="map-feature-section">
                            <button id="map-big-cursor-toggle" class="map-feature-toggle" data-active="false" type="button">
                                <div class="map-feature-icon">
                                    <svg width="24" height="24" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg" version="1.1">
                                        <path fill="#ddd" d="M 5,5 90,30 65,50 95,80 80,95 50,65 30,90 z"/>
                                    </svg>
                                </div>
                                <div class="map-feature-content">
                                    <div class="map-feature-title">Big Cursor</div>
                                    <div class="map-feature-desc">Enlarge cursor size for better visibility and easier tracking</div>
                                </div>
                                <div class="map-toggle-switch">
                                    <span class="map-toggle-slider"></span>
                                </div>
                            </button>

                            <div id="map-big-cursor-status" class="map-status-message" style="display: none;">
                                <div class="map-status-icon">🖱️</div>
                                <div class="map-status-text">Big Cursor is active. Cursor size has been enlarged for better visibility.</div>
                            </div>
                        </div>

                        <!-- Text Magnification Section -->
                        <div class="map-feature-section">
                            <button id="map-text-magnification-toggle" class="map-feature-toggle" data-active="false" type="button">
                                <div class="map-feature-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <circle cx="11" cy="11" r="8"/>
                                        <path d="m21 21-4.35-4.35"/>
                                        <text x="8" y="9" font-family="Arial, sans-serif" font-size="3" font-weight="bold" fill="currentColor">A</text>
                                        <text x="11.5" y="13" font-family="Arial, sans-serif" font-size="2.5" fill="currentColor">a</text>
                                    </svg>
                                </div>
                                <div class="map-feature-content">
                                    <div class="map-feature-title">Text Magnification</div>
                                    <div class="map-feature-desc">Magnify text on hover for better readability and visibility</div>
                                </div>
                                <div class="map-toggle-switch">
                                    <span class="map-toggle-slider"></span>
                                </div>
                            </button>

                            <div id="map-text-magnification-status" class="map-status-message" style="display: none;">
                                <div class="map-status-icon">🔍</div>
                                <div class="map-status-text">Text Magnification is active. Hover over text to see it magnified.</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Preferences Category View (Level 2) - UI #4 -->
                <div id="map-view-preferences" class="map-modal-view" role="region" aria-label="General preferences" data-title="Preferences">
                    <div class="map-view-content">
                        <!-- Dark Mode Toggle -->
                        <div class="map-feature-section">
                            <button id="map-dark-mode-toggle" class="map-feature-toggle" data-active="false" type="button">
                                <div class="map-feature-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/>
                                        <circle cx="19" cy="5" r="0.5" fill="currentColor"/>
                                        <circle cx="17" cy="2" r="0.3" fill="currentColor"/>
                                        <circle cx="22" cy="8" r="0.4" fill="currentColor"/>
                                    </svg>
                                </div>
                                <div class="map-feature-content">
                                    <div class="map-feature-title">Dark mode</div>
                                    <div class="map-feature-desc">Switch to a dark theme for the accessibility menu interface</div>
                                </div>
                                <div class="map-toggle-switch">
                                    <span class="map-toggle-slider"></span>
                                </div>
                            </button>

                            <div id="map-dark-mode-status" class="map-status-message" style="display: none;">
                                <div class="map-status-icon">🌙</div>
                                <div class="map-status-text">Dark interface theme applied!</div>
                            </div>
                        </div>

                        <!-- Menu Position Selection -->
                        <div class="map-feature-section">
                            <button id="map-menu-position-toggle" class="map-feature-toggle" data-active="false" type="button">
                                <div class="map-feature-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M5 9l-3 3 3 3M9 5l3-3 3 3M15 19l-3 3-3-3M19 9l3 3-3 3"/>
                                        <line x1="2" y1="12" x2="22" y2="12"/>
                                        <line x1="12" y1="2" x2="12" y2="22"/>
                                    </svg>
                                </div>
                                <div class="map-feature-content">
                                    <div class="map-feature-title">Menu Position</div>
                                    <div class="map-feature-desc">Choose where the accessibility menu button appears on screen</div>
                                </div>
                                <div class="map-category-arrow">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M9 18l6-6-6-6"/>
                                    </svg>
                                </div>
                            </button>

                            <div id="map-menu-position-content" class="map-feature-controls" style="display: none;">
                                <div class="map-language-options">
                                    <div class="map-position-grid">
                                        <!-- Top Row -->
                                        <div class="map-language-option" data-position="top-left">
                                            <div class="map-language-info">
                                                <div class="map-language-flag">↖</div>
                                                <div class="map-language-details">
                                                    <span class="map-language-name">Top Left</span>
                                                </div>
                                            </div>
                                            <div class="map-language-status">
                                                <div class="map-language-check">
                                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <polyline points="20,6 9,17 4,12"/>
                                                    </svg>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="map-language-option" data-position="top-right">
                                            <div class="map-language-info">
                                                <div class="map-language-flag">↗</div>
                                                <div class="map-language-details">
                                                    <span class="map-language-name">Top Right</span>
                                                </div>
                                            </div>
                                            <div class="map-language-status">
                                                <div class="map-language-check">
                                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <polyline points="20,6 9,17 4,12"/>
                                                    </svg>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Bottom Row -->
                                        <div class="map-language-option" data-position="bottom-left">
                                            <div class="map-language-info">
                                                <div class="map-language-flag">↙</div>
                                                <div class="map-language-details">
                                                    <span class="map-language-name">Bottom Left</span>
                                                </div>
                                            </div>
                                            <div class="map-language-status">
                                                <div class="map-language-check">
                                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <polyline points="20,6 9,17 4,12"/>
                                                    </svg>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="map-language-option active" data-position="bottom-right">
                                            <div class="map-language-info">
                                                <div class="map-language-flag">↘</div>
                                                <div class="map-language-details">
                                                    <span class="map-language-name">Bottom Right</span>
                                                </div>
                                            </div>
                                            <div class="map-language-status">
                                                <div class="map-language-check">
                                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <polyline points="20,6 9,17 4,12"/>
                                                    </svg>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div id="map-menu-position-status" class="map-status-message" style="display: none;">
                                    <div class="map-status-icon">📍</div>
                                    <div class="map-status-text">Menu position updated!</div>
                                </div>
                            </div>
                        </div>

                        <!-- Language Selection -->
                        <div class="map-feature-section">
                            <button id="map-language-toggle" class="map-feature-toggle" data-active="false" type="button">
                                <div class="map-feature-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12.87 15.07l-2.54-2.51.03-.03A17.52 17.52 0 0014.07 6H17V4h-7V2H8v2H1v2h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z"/>
                                    </svg>
                                </div>
                                <div class="map-feature-content">
                                    <div class="map-feature-title">Language</div>
                                    <div class="map-feature-desc">Choose your preferred interface language</div>
                                </div>
                                <div class="map-category-arrow">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M9 18l6-6-6-6"/>
                                    </svg>
                                </div>
                            </button>

                            <div id="map-language-content" class="map-feature-controls" style="display: none;">
                                <div class="map-language-options">
                                    <div class="map-language-option active" data-language="en">
                                        <div class="map-language-info">
                                            <div class="map-language-flag">🇺🇸</div>
                                            <div class="map-language-details">
                                                <span class="map-language-name">English</span>
                                                <span class="map-language-native">English</span>
                                            </div>
                                        </div>
                                        <div class="map-language-status">
                                            <div class="map-language-check">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <polyline points="20,6 9,17 4,12"/>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="map-language-option" data-language="fr">
                                        <div class="map-language-info">
                                            <div class="map-language-flag">🇫🇷</div>
                                            <div class="map-language-details">
                                                <span class="map-language-name">French</span>
                                                <span class="map-language-native">Français</span>
                                            </div>
                                        </div>
                                        <div class="map-language-status">
                                            <div class="map-language-check">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <polyline points="20,6 9,17 4,12"/>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="map-language-option" data-language="es">
                                        <div class="map-language-info">
                                            <div class="map-language-flag">🇪🇸</div>
                                            <div class="map-language-details">
                                                <span class="map-language-name">Spanish</span>
                                                <span class="map-language-native">Español</span>
                                            </div>
                                        </div>
                                        <div class="map-language-status">
                                            <div class="map-language-check">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <polyline points="20,6 9,17 4,12"/>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="map-language-option" data-language="de">
                                        <div class="map-language-info">
                                            <div class="map-language-flag">🇩🇪</div>
                                            <div class="map-language-details">
                                                <span class="map-language-name">German</span>
                                                <span class="map-language-native">Deutsch</span>
                                            </div>
                                        </div>
                                        <div class="map-language-status">
                                            <div class="map-language-check">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <polyline points="20,6 9,17 4,12"/>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div id="map-language-status" class="map-status-message" style="display: none;">
                                    <div class="map-status-icon">🌐</div>
                                    <div class="map-status-text">Language preference updated!</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Colors Category View (Level 2) - UI #5 -->
                <div id="map-view-colors" class="map-modal-view" role="region" aria-label="Color accessibility options" data-title="Contrast & Colors">
                    <div class="map-view-content">
                        <!-- Visual Themes Section -->
                        <div class="map-feature-section">
                            <button id="map-contrast-themes-toggle" class="map-feature-toggle" data-active="false" type="button">
                                <div class="map-feature-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                                        <circle cx="12" cy="12" r="3"/>
                                    </svg>
                                </div>
                                <div class="map-feature-content">
                                    <div class="map-feature-title">Visual Themes</div>
                                    <div class="map-feature-desc">Choose a visual style that works best for you</div>
                                </div>
                                <div class="map-category-arrow">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M9 18l6-6-6-6"/>
                                    </svg>
                                </div>
                            </button>

                            <div id="map-contrast-themes-content" class="map-feature-controls" style="display: none;">
                                <div class="map-theme-selector">
                                    <!-- Theme Preview Card -->
                                    <div class="map-theme-preview-card">
                                        <!-- Navigation Arrows -->
                                        <button id="map-theme-prev" class="map-theme-nav map-theme-nav-prev" type="button" aria-label="Previous theme (applies immediately)">
                                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                                <path d="M15.41 16.59L10.83 12l4.58-4.59L14 6l-6 6 6 6 1.41-1.41z"/>
                                            </svg>
                                        </button>

                                        <button id="map-theme-next" class="map-theme-nav map-theme-nav-next" type="button" aria-label="Next theme (applies immediately)">
                                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                                <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                                            </svg>
                                        </button>

                                        <!-- Theme Icon Preview -->
                                        <div class="map-theme-icon-preview-container">
                                            <div id="map-theme-icon-preview" class="map-theme-icon-preview" data-theme="normal">
                                                <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
                                                    <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                                                </svg>
                                            </div>
                                        </div>

                                        <!-- Theme Info -->
                                        <div class="map-theme-info">
                                            <h4 id="map-theme-name" class="map-theme-title">Default</h4>
                                        </div>
                                    </div>

                                    <!-- Theme Dots Indicator -->
                                    <div class="map-theme-dots">
                                        <button class="map-theme-dot active" data-theme="normal" aria-label="Apply default theme"></button>
                                        <button class="map-theme-dot" data-theme="monochrome" aria-label="Monochrome theme"></button>
                                        <button class="map-theme-dot" data-theme="low-saturation" aria-label="Low saturation theme"></button>
                                        <button class="map-theme-dot" data-theme="high-saturation" aria-label="High saturation theme"></button>
                                        <button class="map-theme-dot" data-theme="dark" aria-label="Dark theme"></button>
                                        <button class="map-theme-dot" data-theme="high-contrast" aria-label="High contrast theme"></button>
                                        <button class="map-theme-dot" data-theme="sepia" aria-label="Sepia theme"></button>
                                        <button class="map-theme-dot" data-theme="colorblind" aria-label="Color blind friendly theme"></button>
                                    </div>
                                </div>

                                <div id="map-contrast-theme-status" class="map-status-message" style="display: none;">
                                    <div class="map-status-icon">🎨</div>
                                    <div class="map-status-text">Theme applied successfully. The visual appearance of the website has been updated.</div>
                                </div>
                            </div>
                        </div>

                        <!-- Color Studio Section -->
                        <div class="map-feature-section">
                            <button id="map-custom-theme-toggle" class="map-feature-toggle" data-active="false" type="button">
                                <div class="map-feature-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <circle cx="13.5" cy="6.5" r=".5"/>
                                        <circle cx="17.5" cy="10.5" r=".5"/>
                                        <circle cx="8.5" cy="7.5" r=".5"/>
                                        <circle cx="6.5" cy="12.5" r=".5"/>
                                        <path d="M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z"/>
                                    </svg>
                                </div>
                                <div class="map-feature-content">
                                    <div class="map-feature-title">Color Studio</div>
                                    <div class="map-feature-desc">Design your perfect color palette with live preview</div>
                                </div>
                                <div class="map-category-arrow">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M9 18l6-6-6-6"/>
                                    </svg>
                                </div>
                            </button>

                            <div id="map-custom-theme-content" class="map-feature-controls" style="display: none;">
                                <div class="map-color-studio-compact">
                                    <!-- Text Color Row -->
                                    <div class="map-color-row">
                                        <div class="map-color-label">
                                            <div class="map-color-icon">
                                                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                                    <polyline points="14,2 14,8 20,8"/>
                                                    <line x1="16" y1="13" x2="8" y2="13"/>
                                                    <line x1="16" y1="17" x2="8" y2="17"/>
                                                    <polyline points="10,9 9,9 8,9"/>
                                                </svg>
                                            </div>
                                            <div class="map-color-info">
                                                <span class="map-color-title">Text</span>
                                                <span class="map-color-desc">Body text & paragraphs</span>
                                            </div>
                                        </div>
                                        <div class="map-color-controls">
                                            <div class="map-color-picker-wrapper">
                                                <input type="color" id="map-custom-text-color" class="map-color-picker-compact" value="#374151">
                                                <button type="button" class="map-color-reset-btn" data-target="map-custom-text-color" aria-label="Reset text color" style="display: none;">
                                                    <svg width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
                                                        <path d="M18 6L6 18"/>
                                                        <path d="M6 6l12 12"/>
                                                    </svg>
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Background Color Row -->
                                    <div class="map-color-row">
                                        <div class="map-color-label">
                                            <div class="map-color-icon">
                                                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                                                    <circle cx="8.5" cy="8.5" r="1.5"/>
                                                    <polyline points="21,15 16,10 5,21"/>
                                                </svg>
                                            </div>
                                            <div class="map-color-info">
                                                <span class="map-color-title">Background</span>
                                                <span class="map-color-desc">Page & content areas</span>
                                            </div>
                                        </div>
                                        <div class="map-color-controls">
                                            <div class="map-color-picker-wrapper">
                                                <input type="color" id="map-custom-bg-color" class="map-color-picker-compact" value="#ffffff">
                                                <button type="button" class="map-color-reset-btn" data-target="map-custom-bg-color" aria-label="Reset background color" style="display: none;">
                                                    <svg width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
                                                        <path d="M18 6L6 18"/>
                                                        <path d="M6 6l12 12"/>
                                                    </svg>
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Links Color Row -->
                                    <div class="map-color-row">
                                        <div class="map-color-label">
                                            <div class="map-color-icon">
                                                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"/>
                                                    <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"/>
                                                </svg>
                                            </div>
                                            <div class="map-color-info">
                                                <span class="map-color-title">Links</span>
                                                <span class="map-color-desc">Hyperlinks & buttons</span>
                                            </div>
                                        </div>
                                        <div class="map-color-controls">
                                            <div class="map-color-picker-wrapper">
                                                <input type="color" id="map-custom-link-color" class="map-color-picker-compact" value="#6366f1">
                                                <button type="button" class="map-color-reset-btn" data-target="map-custom-link-color" aria-label="Reset link color" style="display: none;">
                                                    <svg width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
                                                        <path d="M18 6L6 18"/>
                                                        <path d="M6 6l12 12"/>
                                                    </svg>
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Headings Color Row -->
                                    <div class="map-color-row">
                                        <div class="map-color-label">
                                            <div class="map-color-icon">
                                                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path d="M6 12h12"/>
                                                    <path d="M6 20V4"/>
                                                    <path d="M18 20V4"/>
                                                </svg>
                                            </div>
                                            <div class="map-color-info">
                                                <span class="map-color-title">Headings</span>
                                                <span class="map-color-desc">Titles & headers</span>
                                            </div>
                                        </div>
                                        <div class="map-color-controls">
                                            <div class="map-color-picker-wrapper">
                                                <input type="color" id="map-custom-heading-color" class="map-color-picker-compact" value="#111827">
                                                <button type="button" class="map-color-reset-btn" data-target="map-custom-heading-color" aria-label="Reset heading color" style="display: none;">
                                                    <svg width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
                                                        <path d="M18 6L6 18"/>
                                                        <path d="M6 6l12 12"/>
                                                    </svg>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div id="map-custom-theme-status" class="map-status-message" style="display: none;">
                                    <div class="map-status-icon">✨</div>
                                    <div class="map-status-text">Custom theme applied successfully!</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div> <!-- Close map-modal-views-container -->
        </div>
    </div>
</div>
